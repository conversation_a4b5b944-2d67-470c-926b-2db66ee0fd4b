<?php

namespace Tests\Unit\Core\Infrastructure\FLM;

use Tests\TestCase;
use App\Core\Infrastructure\FLM\Services\AreaService;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Response;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;

class AreaServiceTest extends TestCase
{
    private AreaService $areaService;
    private MockHandler $mockHandler;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock Guzzle client
        $this->mockHandler = new MockHandler();
        $handlerStack = HandlerStack::create($this->mockHandler);
        $mockClient = new Client(['handler' => $handlerStack]);

        $this->areaService = new AreaService($mockClient);
    }

    protected function tearDown(): void
    {
        parent::tearDown();
    }


    public function test_returns_area_data_from_api()
    {

        $mockResponse = json_encode([
            'data' => [
                ['id' => 1, 'name' => 'Area A'],
                ['id' => 2, 'name' => 'Area B']
            ]
        ]);

        $this->mockHandler->append(
            new Response(200, [], $mockResponse)
        );

        // Act
        $areas = $this->areaService->getAreas();

        // Assert
        $this->assertIsArray($areas);
        $this->assertCount(2, $areas);
        $this->assertEquals('Area A', $areas[0]['name']);
        $this->assertEquals('Area B', $areas[1]['name']);
    }

    public function test_returns_an_exception_api_error()
    {

        $this->mockHandler->append(
            new Response(500, [], json_encode(['error' => 'An unexpected error occurred']))
        );

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('An unexpected error occurred');

        $this->areaService->getAreas();
    }
}
