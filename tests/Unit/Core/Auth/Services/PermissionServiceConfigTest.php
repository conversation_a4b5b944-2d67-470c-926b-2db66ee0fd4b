<?php

namespace Tests\Unit\Core\Auth\Services;

use App\Core\Auth\Constants\Permission;
use App\Core\Auth\Models\AuthUser;
use App\Core\Auth\Services\PermissionService;
use App\Core\Auth\ValueObjects\Role;
use Tests\TestCase;

class PermissionServiceConfigTest extends TestCase
{
    private PermissionService $permissionService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->permissionService = new PermissionService();
    }

    public function test_can_get_all_permissions_from_config(): void
    {
        $permissions = $this->permissionService->getAllPermissions();

        $this->assertContains(Permission::WAREHOUSE_VIEW, $permissions);
        $this->assertContains(Permission::WAREHOUSE_CREATE, $permissions);
        $this->assertContains(Permission::INVENTORY_VIEW, $permissions);
        $this->assertContains(Permission::INVENTORY_CREATE, $permissions);
    }

    public function test_can_get_role_permissions_from_config(): void
    {
        $adminPermissions = $this->permissionService->getRolePermissions(Role::ADMIN);
        $warehouseManagerPermissions = $this->permissionService->getRolePermissions(Role::WAREHOUSE_MANAGER);

        // Admin should have all permissions
        $this->assertContains(Permission::WAREHOUSE_DELETE, $adminPermissions);
        $this->assertContains(Permission::INVENTORY_DELETE, $adminPermissions);

        // Warehouse Manager should not have delete permissions
        $this->assertNotContains(Permission::WAREHOUSE_DELETE, $warehouseManagerPermissions);
        $this->assertNotContains(Permission::INVENTORY_DELETE, $warehouseManagerPermissions);

        // But should have view and update permissions
        $this->assertContains(Permission::WAREHOUSE_VIEW, $warehouseManagerPermissions);
        $this->assertContains(Permission::WAREHOUSE_UPDATE, $warehouseManagerPermissions);
    }

    public function test_permission_exists_check(): void
    {
        $this->assertTrue($this->permissionService->permissionExists(Permission::WAREHOUSE_VIEW));
        $this->assertFalse($this->permissionService->permissionExists('non.existent.permission'));
    }

    public function test_admin_has_all_permissions(): void
    {
        $admin = new AuthUser(1, [], [new Role(Role::ADMIN)]);

        $this->assertTrue($this->permissionService->hasPermission($admin, Permission::WAREHOUSE_DELETE));
        $this->assertTrue($this->permissionService->hasPermission($admin, Permission::INVENTORY_DELETE));
        $this->assertTrue($this->permissionService->hasPermission($admin, Permission::WAREHOUSE_CREATE));
    }

    public function test_warehouse_manager_permissions(): void
    {
        $manager = new AuthUser(2, [], [new Role(Role::WAREHOUSE_MANAGER)]);

        // Should have these permissions
        $this->assertTrue($this->permissionService->hasPermission($manager, Permission::WAREHOUSE_VIEW));
        $this->assertTrue($this->permissionService->hasPermission($manager, Permission::WAREHOUSE_UPDATE));
        $this->assertTrue($this->permissionService->hasPermission($manager, Permission::INVENTORY_CREATE));

        // Should NOT have these permissions
        $this->assertFalse($this->permissionService->hasPermission($manager, Permission::WAREHOUSE_DELETE));
        $this->assertFalse($this->permissionService->hasPermission($manager, Permission::INVENTORY_DELETE));
        $this->assertFalse($this->permissionService->hasPermission($manager, Permission::WAREHOUSE_CREATE));
    }

    public function test_fulfillment_officer_permissions(): void
    {
        $officer = new AuthUser(3, [], [new Role(Role::FULFILLMENT_OFFICER)]);

        // Should have read-only permissions
        $this->assertTrue($this->permissionService->hasPermission($officer, Permission::WAREHOUSE_VIEW));
        $this->assertTrue($this->permissionService->hasPermission($officer, Permission::INVENTORY_VIEW));

        // Should NOT have write permissions
        $this->assertFalse($this->permissionService->hasPermission($officer, Permission::WAREHOUSE_CREATE));
        $this->assertFalse($this->permissionService->hasPermission($officer, Permission::INVENTORY_CREATE));
        $this->assertFalse($this->permissionService->hasPermission($officer, Permission::WAREHOUSE_UPDATE));
    }

    public function test_permission_is_valid_with_dependency_injection(): void
    {
        // Test the improved isValid method with dependency injection
        $mockPermissionService = $this->createMock(PermissionService::class);
        $mockPermissionService->expects($this->once())
            ->method('permissionExists')
            ->with('test.permission')
            ->willReturn(true);

        $result = Permission::isValid('test.permission', $mockPermissionService);
        $this->assertTrue($result);
    }

    public function test_permission_is_valid_without_dependency_injection(): void
    {
        // Test backward compatibility - should still work without injecting service
        $this->assertTrue(Permission::isValid(Permission::WAREHOUSE_VIEW));
        $this->assertFalse(Permission::isValid('non.existent.permission'));
    }

    public function test_admin_permissions_with_empty_role_permissions(): void
    {
        // Test the fix for empty array spread operator issue
        $admin = new AuthUser(1, [], [new Role(Role::ADMIN)]);

        // Backup original config
        $originalConfig = config('permissions.role_permissions');

        // Temporarily override config to simulate empty role permissions
        config(['permissions.role_permissions' => []]);

        // This should not throw an error and should return empty array
        $permissions = $this->permissionService->getUserPermissions($admin);
        $this->assertIsArray($permissions);
        $this->assertEmpty($permissions);

        // Restore original config
        config(['permissions.role_permissions' => $originalConfig]);
    }

    public function test_admin_permissions_with_valid_role_permissions(): void
    {
        // Test normal case still works
        $admin = new AuthUser(1, [], [new Role(Role::ADMIN)]);

        $permissions = $this->permissionService->getUserPermissions($admin);

        // Admin should have all permissions
        $this->assertContains(Permission::WAREHOUSE_VIEW, $permissions);
        $this->assertContains(Permission::WAREHOUSE_CREATE, $permissions);
        $this->assertContains(Permission::INVENTORY_VIEW, $permissions);
        $this->assertContains(Permission::INVENTORY_CREATE, $permissions);
    }
}
