<?php

namespace Tests\Unit\Core\Auth\Services;

use App\Core\Auth\Models\AuthUser;
use App\Core\Auth\Services\AuthContext;
use App\Core\Auth\ValueObjects\Role;
use PHPUnit\Framework\TestCase;

class AuthContextTest extends TestCase
{
    private AuthContext $authContext;

    protected function setUp(): void
    {
        parent::setUp();
        $this->authContext = new AuthContext();
    }

    public function test_initially_no_user_is_authenticated(): void
    {
        $this->assertFalse($this->authContext->isAuthenticated());
        $this->assertNull($this->authContext->getUser());
        $this->assertNull($this->authContext->getUserId());
    }

    public function test_can_set_and_get_user(): void
    {
        $user = new AuthUser(123, []);
        $this->authContext->setUser($user);

        $this->assertTrue($this->authContext->isAuthenticated());
        $this->assertSame($user, $this->authContext->getUser());
        $this->assertEquals(123, $this->authContext->getUserId());
    }

    public function test_can_check_if_user_has_role(): void
    {
        $this->assertFalse($this->authContext->hasRole(Role::ADMIN));

        $user = new AuthUser(123, [], [new Role(Role::ADMIN)]);
        $this->authContext->setUser($user);

        $this->assertTrue($this->authContext->hasRole(Role::ADMIN));
        $this->assertFalse($this->authContext->hasRole(Role::WAREHOUSE_MANAGER));
    }

    public function test_can_check_if_user_has_any_role(): void
    {
        $this->assertFalse($this->authContext->hasAnyRole([Role::ADMIN, Role::WAREHOUSE_MANAGER]));

        $user = new AuthUser(123, [], [new Role(Role::ADMIN)]);
        $this->authContext->setUser($user);

        $this->assertTrue($this->authContext->hasAnyRole([Role::ADMIN, Role::WAREHOUSE_MANAGER]));
        $this->assertFalse($this->authContext->hasAnyRole([Role::WAREHOUSE_MANAGER, Role::STOCK_CONTROLLER]));
    }

    public function test_can_check_if_user_has_all_roles(): void
    {
        $this->assertFalse($this->authContext->hasAllRoles([Role::ADMIN, Role::WAREHOUSE_MANAGER]));

        $user = new AuthUser(123, [], [
            new Role(Role::ADMIN),
            new Role(Role::WAREHOUSE_MANAGER)
        ]);
        $this->authContext->setUser($user);

        $this->assertTrue($this->authContext->hasAllRoles([Role::ADMIN, Role::WAREHOUSE_MANAGER]));
        $this->assertFalse($this->authContext->hasAllRoles([Role::ADMIN, Role::STOCK_CONTROLLER]));
    }

    public function test_can_clear_authenticated_user(): void
    {
        $user = new AuthUser(123, []);
        $this->authContext->setUser($user);
        $this->assertTrue($this->authContext->isAuthenticated());

        $this->authContext->clear();
        $this->assertFalse($this->authContext->isAuthenticated());
        $this->assertNull($this->authContext->getUser());
    }
}
