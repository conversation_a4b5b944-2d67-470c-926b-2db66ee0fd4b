<?php

namespace Tests\Unit\Core\Auth\ValueObjects;

use App\Core\Auth\ValueObjects\Role;
use InvalidArgumentException;
use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\Attributes\DataProvider;

class RoleTest extends TestCase
{
    #[DataProvider('validRoleProvider')]
    public function test_can_create_valid_role(string $roleName): void
    {
        $role = new Role($roleName);
        $this->assertEquals($roleName, $role->getName());
    }

    public static function validRoleProvider(): array
    {
        return [
            'admin role' => [Role::ADMIN],
            'warehouse manager role' => [Role::WAREHOUSE_MANAGER],
            'stock controller role' => [Role::STOCK_CONTROLLER],
        ];
    }

    public function test_cannot_create_invalid_role(): void
    {
        $this->expectException(InvalidArgumentException::class);
        new Role('invalid_role');
    }

    public function test_roles_with_same_name_are_equal(): void
    {
        $role1 = new Role(Role::ADMIN);
        $role2 = new Role(Role::ADMIN);

        $this->assertTrue($role1->equals($role2));
    }

    public function test_roles_with_different_names_are_not_equal(): void
    {
        $role1 = new Role(Role::ADMIN);
        $role2 = new Role(Role::WAREHOUSE_MANAGER);

        $this->assertFalse($role1->equals($role2));
    }

    public function test_can_create_role_from_string(): void
    {
        $role = Role::fromString(Role::ADMIN);
        $this->assertEquals(Role::ADMIN, $role->getName());
    }

    public function test_can_check_if_role_is_valid(): void
    {
        $this->assertTrue(Role::isValid(Role::ADMIN));
        $this->assertFalse(Role::isValid('invalid_role'));
    }

    public function test_can_convert_role_to_string(): void
    {
        $role = new Role(Role::ADMIN);
        $this->assertEquals(Role::ADMIN, (string) $role);
    }
}
