<?php

namespace Tests\Unit\Core\Interfaces\Http\Controllers;

use Tests\TestCase;
use App\Core\Contracts\FLM\AreaServiceInterface;
use App\Core\Interfaces\Http\Controllers\FLM\AreaAPIController;
use Mockery;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AreaAPIControllerTest extends TestCase
{
    private $areaServiceMock;
    private $controller;

    protected function setUp(): void
    {
        parent::setUp();
        $this->areaServiceMock = Mockery::mock(AreaServiceInterface::class);
        $this->controller = new AreaAPIController($this->areaServiceMock);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_index_with_no_query_parameters(): void
    {
        $areas =  [
            ['id' => 1, 'name' => 'Area A'],
            ['id' => 2, 'name' => 'Area B'],
            ['id' => 3, 'name' => 'Area C'],
            ['id' => 4, 'name' => 'Area D']
        ];
        $request = new Request();
        // Mock the service
        $this->areaServiceMock->shouldReceive('getAreas')
            ->once()
            ->andReturn($areas);

        // Act
        $response = $this->controller->index($request);

        // Assert the structure and values

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('Areas retrieved successfully', $responseData['message']);
        $this->assertEquals($areas, $responseData['data']);
    }

    public function test_index_with_query_parameters(): void
    {
        $queryParams = ['name' => 'C'];
        $request = new Request($queryParams);

        // Mock the service
        $this->areaServiceMock->shouldReceive('getAreas')
            ->with('C')
            ->once()
            ->andReturn([['id' => 3, 'name' => 'Area C']]);

        // Act
        $response = $this->controller->index($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        // Assert the structure and values
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('Areas retrieved successfully', $responseData['message']);
        $this->assertEquals([['id' => 3, 'name' => 'Area C']], $responseData['data']);
    }

    public function test_index_method_with_service_exception(): void
    {
        $request = new Request();
        $this->areaServiceMock->shouldReceive('getAreas')
            ->once()
            ->andThrow(new \Exception('Service error'));

        // Expect exception to be thrown
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Service error');

        // Act
        $this->controller->index($request);
    }
}
