<?php

namespace Tests\Unit\WarehouseManagement\Domain\Entities;

use PHPUnit\Framework\TestCase;
use App\WarehouseManagement\Domain\Entities\Warehouse;
use App\WarehouseManagement\Domain\ValueObjects\Address;
use App\WarehouseManagement\Domain\ValueObjects\Manager;
use DomainException;

class WarehouseTest extends TestCase {
    private const TEST_WAREHOUSE_ID_1 = 'test001a';

    public function test_create_valid_warehouse(): void {
        // Arrange
        $name = 'Test Warehouse';
        $managerId = 123;
        $managerName = 'John Doe';
        $totalSpace = 1000.0;
        $area = 'Test Area';
        $building = 'Building A';
        $floor = 2;
        $directions = 'Test directions';
        $id = self::TEST_WAREHOUSE_ID_1;

        $manager = new Manager($managerId, $managerName);
        $address = new Address($area, $building, $floor, $directions);

        // Act
        $warehouse = new Warehouse(
            $name,
            $manager,
            $totalSpace,
            $address,
            $id
        );

        // Assert
        $this->assertEquals($name, $warehouse->getName());
        $this->assertEquals($manager, $warehouse->getManager());
        $this->assertEquals($managerId, $warehouse->getManagerId());
        $this->assertEquals($managerName, $warehouse->getManagerName());
        $this->assertEquals($totalSpace, $warehouse->getTotalSpace());
        $this->assertEquals($address, $warehouse->getAddress());
        $this->assertEquals($id, $warehouse->getId(), 'Warehouse should have the provided ID');
    }

    public function test_create_warehouse_with_empty_name_throws_exception(): void {
        // Arrange
        $name = '';
        $manager = new Manager(123, 'John Doe');
        $address = new Address('Area', 'Building', 1, 'Directions');

        // Assert & Act
        $this->expectException(DomainException::class);
        $this->expectExceptionMessage('Warehouse name cannot be empty');

        new Warehouse($name, $manager, 100.0, $address, self::TEST_WAREHOUSE_ID_1);
    }

    public function test_create_warehouse_with_zero_space_throws_exception(): void {
        // Arrange
        $manager = new Manager(123, 'John Doe');
        $address = new Address('Area', 'Building', 1, 'Directions');

        // Assert & Act
        $this->expectException(DomainException::class);
        $this->expectExceptionMessage('Total space must be greater than zero');

        new Warehouse('Test Warehouse', $manager, 0, $address, self::TEST_WAREHOUSE_ID_1);
    }

    public function test_create_warehouse_with_negative_space_throws_exception(): void {
        // Arrange
        $manager = new Manager(123, 'John Doe');
        $address = new Address('Area', 'Building', 1, 'Directions');

        // Assert & Act
        $this->expectException(DomainException::class);
        $this->expectExceptionMessage('Total space must be greater than zero');

        new Warehouse('Test Warehouse', $manager, -10.5, $address, self::TEST_WAREHOUSE_ID_1);
    }

    public function createValidWarehouse(): Warehouse {
        $manager = new Manager(123, 'John Doe');
        $address = new Address('Area', 'Building', 1, 'Directions');

        return new Warehouse(
            'Test Warehouse',
            $manager,
            1000,
            $address,
            self::TEST_WAREHOUSE_ID_1
        );
    }
}
