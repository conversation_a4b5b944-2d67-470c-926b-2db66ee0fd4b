<?php

namespace Tests\Unit\WarehouseManagement\Domain\Entities;

use PHPUnit\Framework\TestCase;
use App\WarehouseManagement\Domain\Entities\StorageUnit;
use DomainException;

class StorageUnitTest extends TestCase {
    private const TEST_STORAGE_UNIT_ID_1 = '00000000';
    private const TEST_WAREHOUSE_ID_1 = 'test001a';

    public function test_create_valid_storage_unit(): void {
        // Arrange
        $warehouseId = 1;
        $typeId = 1;

        $storageUnit = new StorageUnit($warehouseId, $typeId, null, self::TEST_STORAGE_UNIT_ID_1);

        // Assert
        $this->assertEquals(self::TEST_STORAGE_UNIT_ID_1, $storageUnit->getId(), 'Storage unit should have the provided ID');
        $this->assertEquals($warehouseId, $storageUnit->getWarehouseId());
        $this->assertEquals($typeId, $storageUnit->getTypeId());
    }
}
