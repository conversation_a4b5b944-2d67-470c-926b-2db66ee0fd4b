<?php

namespace Tests\Unit\WarehouseManagement\Domain\ValueObjects;

use PHPUnit\Framework\TestCase;
use App\WarehouseManagement\Domain\ValueObjects\Address;
use InvalidArgumentException;
use PHPUnit\Framework\Attributes\DataProvider;

class AddressTest extends TestCase
{
    public function test_create_valid_address(): void
    {
        // Arrange & Act
        $area = 'Test Area';
        $building = 'Building A';
        $floor = 2;
        $directions = 'Test directions';
        
        $address = new Address($area, $building, $floor, $directions);
        
        // Assert
        $this->assertEquals($area, $address->getArea());
        $this->assertEquals($building, $address->getBuilding());
        $this->assertEquals($floor, $address->getFloor());
        $this->assertEquals($directions, $address->getDirections());
    }
    
    public function test_create_address_with_empty_area_throws_exception(): void
    {
        // Arrange
        $area = '';
        $building = 'Building A';
        $floor = 2;
        $directions = 'Test directions';
        
        // Assert & Act
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Area cannot be empty');
        
        new Address($area, $building, $floor, $directions);
    }
    
    public function test_create_address_with_empty_building_throws_exception(): void
    {
        // Arrange
        $area = 'Test Area';
        $building = '';
        $floor = 2;
        $directions = 'Test directions';
        
        // Assert & Act
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Building cannot be empty');
        
        new Address($area, $building, $floor, $directions);
    }
    
    public function test_create_address_with_negative_floor_throws_exception(): void
    {
        // Arrange
        $area = 'Test Area';
        $building = 'Building A';
        $floor = -1;
        $directions = 'Test directions';
        
        // Assert & Act
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Floor must be a positive number');
        
        new Address($area, $building, $floor, $directions);
    }
    
    public function test_create_address_with_empty_directions_throws_exception(): void
    {
        // Arrange
        $area = 'Test Area';
        $building = 'Building A';
        $floor = 2;
        $directions = '';

        // Assert & Act
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Directions cannot be empty');
        
        // Act
        new Address($area, $building, $floor, $directions);

    }
    
    public function test_addresses_with_same_values_are_equal(): void
    {
        // Arrange
        $address1 = new Address('Test Area', 'Building A', 2, 'Test directions');
        $address2 = new Address('Test Area', 'Building A', 2, 'Test directions');
        
        // Act & Assert
        $this->assertTrue($address1->equals($address2));
    }
    
    /**
     * Test addresses with different values are not equal
     */
    #[DataProvider('addressInequalityProvider')]
    public function test_addresses_with_different_values_are_not_equal(
        string $area1, string $building1, int $floor1, string $directions1,
        string $area2, string $building2, int $floor2, string $directions2
    ): void {
        // Arrange
        $address1 = new Address($area1, $building1, $floor1, $directions1);
        $address2 = new Address($area2, $building2, $floor2, $directions2);
        
        // Act & Assert
        $this->assertFalse($address1->equals($address2));
    }
    
    public static function addressInequalityProvider(): array
    {
        $baseArea = 'Test Area';
        $baseBuilding = 'Building A';
        $baseFloor = 2;
        $baseDirections = 'Test directions';
        
        return [
            'different area' => [
                $baseArea, $baseBuilding, $baseFloor, $baseDirections,
                'Different Area', $baseBuilding, $baseFloor, $baseDirections
            ],
            'different building' => [
                $baseArea, $baseBuilding, $baseFloor, $baseDirections,
                $baseArea, 'Building B', $baseFloor, $baseDirections
            ],
            'different floor' => [
                $baseArea, $baseBuilding, $baseFloor, $baseDirections,
                $baseArea, $baseBuilding, 3, $baseDirections
            ],
            'different directions' => [
                $baseArea, $baseBuilding, $baseFloor, $baseDirections,
                $baseArea, $baseBuilding, $baseFloor, 'Different directions'
            ]
        ];
    }
}
