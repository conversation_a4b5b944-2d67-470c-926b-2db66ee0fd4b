<?php

namespace Tests\Unit\WarehouseManagement\Application\Services;

use Tests\TestCase;
use App\WarehouseManagement\Application\Services\StorageUnitService;
use App\WarehouseManagement\Domain\Repositories\StorageUnitRepositoryInterface;
use App\WarehouseManagement\Domain\Entities\StorageUnit;
use Mockery;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use App\WarehouseManagement\Application\Services\WarehouseService;
use App\WarehouseManagement\Domain\Entities\Warehouse;
use App\WarehouseManagement\Domain\ValueObjects\Address;
use App\WarehouseManagement\Domain\ValueObjects\Manager;
use App\Core\Utils\IdGenerator;
use App\WarehouseManagement\Application\DTOs\StorageUnitDTO;
use App\Settings\Domain\Entities\StorageUnitType as StorageUnitTypeEntity;
use App\Settings\Domain\ValueObjects\StorageUnitTypeCode;
use Illuminate\Pagination\LengthAwarePaginator;
use PHPUnit\Framework\Attributes\DataProvider;

class StorageUnitServiceTest extends TestCase {
    private $repositoryMock;
    private $service;
    private $warehouseServiceMock;
    private const TEST_WAREHOUSE_ID = '978d3b8d';
    private const TEST_STORAGE_UNIT_ID = '00000000';
    private const TEST_STORAGE_UNIT_ID_2 = '00000001';
    private const TEST_STORAGE_UNIT_ID_3 = '00000002';

    protected function setUp(): void {
        parent::setUp();
        $this->repositoryMock = Mockery::mock(StorageUnitRepositoryInterface::class);
        $this->warehouseServiceMock = Mockery::mock(WarehouseService::class);
        $this->service = new StorageUnitService($this->repositoryMock, $this->warehouseServiceMock);
    }

    private function getValidStorageUnitData($quantity = 1): array {
        return [
            'warehouse_id' => self::TEST_WAREHOUSE_ID,
            'storageUnits' => [
                [
                    'quantity' => $quantity,
                    'type_id' => 1
                ],
                [
                    'quantity' => 1,
                    'type_id' => 2
                ]
            ]
        ];
    }

    public function test_create_storage_unit_success(): void {
        // Arrange
        $data = $this->getValidStorageUnitData(2);

        // Create a warehouse entity
        $warehouseEntity = new Warehouse(
            'Test Warehouse',
            new Manager(1, 'Test Manager'),
            1000,
            new Address('Area', 'Building', 1, 'Directions'),
            $data['warehouse_id']
        );

        // Create a WarehouseDTO to return from the mock
        $warehouseDTO = new \App\WarehouseManagement\Application\DTOs\WarehouseDTO(
            $warehouseEntity,
            [] // Empty array for storage units
        );

        // Mock the warehouse service to return a valid warehouse DTO
        $this->warehouseServiceMock->shouldReceive('getWarehouse')
            ->once()
            ->with($data['warehouse_id'])
            ->andReturn($warehouseDTO);

        static $counter = 0;

        foreach ($data['storageUnits'] as $unit) {
            for ($i = 0; $i < $unit['quantity']; $i++) {
                $id = str_pad(dechex($counter), 8, '0', STR_PAD_LEFT);
                $this->mockIdGenerator($id);
                $counter++;
                $this->repositoryMock->shouldReceive('create')
                    ->once()
                    ->andReturnUsing(function ($storageUnit) use ($id) {
                        $storageUnitEntity = new StorageUnit(
                            $storageUnit->getWarehouseId(),
                            $storageUnit->getTypeId(),
                            '2023-01-15 10:00:00',
                            $id
                        );
                        return new StorageUnitDTO($storageUnitEntity, new StorageUnitTypeEntity('Test Type', new StorageUnitTypeCode('SLF'), 10, 10, 10, 'test.jpg', 1));
                    });
            }
        }

        // Act
        $result = $this->service->createStorageUnit($data);

        // Assert
        $this->assertCount(3, $result);

        // Assert first storage unit (type_id=1)
        $this->assertInstanceOf(StorageUnitDTO::class, $result[0]);
        $this->assertEquals(self::TEST_STORAGE_UNIT_ID, $result[0]->storageUnit->getId());
        $this->assertEquals($data['warehouse_id'], $result[0]->storageUnit->getWarehouseId());
        $this->assertEquals($data['storageUnits'][0]['type_id'], $result[0]->storageUnit->getTypeId());

        // Assert second storage unit (type_id=1)
        $this->assertInstanceOf(StorageUnitDTO::class, $result[1]);
        $this->assertEquals(self::TEST_STORAGE_UNIT_ID_2, $result[1]->storageUnit->getId());
        $this->assertEquals($data['warehouse_id'], $result[1]->storageUnit->getWarehouseId());
        $this->assertEquals($data['storageUnits'][0]['type_id'], $result[1]->storageUnit->getTypeId());

        // Assert third storage unit (type_id=2)
        $this->assertInstanceOf(StorageUnitDTO::class, $result[2]);
        $this->assertEquals(self::TEST_STORAGE_UNIT_ID_3, $result[2]->storageUnit->getId());
        $this->assertEquals($data['warehouse_id'], $result[2]->storageUnit->getWarehouseId());
        $this->assertEquals($data['storageUnits'][1]['type_id'], $result[2]->storageUnit->getTypeId());
    }

    public function test_create_storage_unit_with_nonexistent_warehouse_throws_exception(): void {
        // Arrange
        $data = $this->getValidStorageUnitData();
        $data['warehouse_id'] = self::TEST_WAREHOUSE_ID;

        // Set up the mock expectation for getWarehouse
        $this->warehouseServiceMock->shouldReceive('getWarehouse')
            ->once()
            ->with(self::TEST_WAREHOUSE_ID)
            ->andReturn(null);

        // Assert
        $this->expectException(NotFoundHttpException::class);
        $this->expectExceptionMessage("Warehouse with ID {$data['warehouse_id']} does not exist");

        // Act
        $this->service->createStorageUnit($data);
    }

    /**
     * Helper method to mock the IdGenerator
     */
    private function mockIdGenerator(string $id): void {
        Mockery::mock('alias:' . IdGenerator::class)
            ->shouldReceive('generateUnique')
            ->andReturn($id);
    }

    /**
     * 
     */
    #[DataProvider('typeIdProvider')]
    public function test_paginate_storage_units_success(?int $typeId): void {
        $perPage = 10;
        $page = 1;
        $warehouseId = 1;

        $storageUnit1 = new StorageUnit($warehouseId, 1, null, 'ABC123');
        $storageUnit2 = new StorageUnit($warehouseId, 2, null, 'DEF456');

        // Create collection based on typeId filter
        $items = collect();
        if ($typeId === null || $typeId === 1) {
            $items->push($storageUnit1);
        }
        if ($typeId === null || $typeId === 2) {
            $items->push($storageUnit2);
        }

        $total = $items->count(); // Total items
        $mockPaginator = new LengthAwarePaginator(
            $items,
            $total,
            $perPage,
            $page
        );

        // Mock the repository paginate method (expectation)
        $this->repositoryMock->shouldReceive('paginate')
            ->once()
            ->with($perPage, $page, $warehouseId, $typeId)
            ->andReturn($mockPaginator);

        // Act
        $result = $this->service->paginateStorageUnits($perPage, $page, $warehouseId, $typeId);

        $this->assertInstanceOf(LengthAwarePaginator::class, $result);
        $this->assertEquals($total, $result->count());
        $this->assertEquals($total, $result->total());
        $this->assertEquals($perPage, $result->perPage());
        $this->assertEquals($page, $result->currentPage());

        // Specific assertions based on typeId
        if ($typeId === null) {
            $this->assertEquals(2, $result->count());
            $this->assertInstanceOf(StorageUnit::class, $result->items()[0]);
            $this->assertInstanceOf(StorageUnit::class, $result->items()[1]);
            $this->assertSame($storageUnit1, $result->items()[0]);
            $this->assertSame($storageUnit2, $result->items()[1]);
        } elseif ($typeId === 1) {
            $this->assertEquals(1, $result->count());
            $this->assertInstanceOf(StorageUnit::class, $result->items()[0]);
            $this->assertSame($storageUnit1, $result->items()[0]);
        } elseif ($typeId === 2) {
            $this->assertEquals(1, $result->count());
            $this->assertInstanceOf(StorageUnit::class, $result->items()[0]);
            $this->assertSame($storageUnit2, $result->items()[0]);
        } elseif ($typeId === 3) {
            $this->assertEquals(0, $result->count());
        }
    }

    /**
     * Test paginate storage units with different page
     */
    #[DataProvider('typeIdProvider')]
    public function test_paginate_storage_unit_with_different_page(?int $typeId): void {
        $perPage = 10;
        $page = 2;
        $warehouseId = 1;

        $storageUnit3 = new StorageUnit($warehouseId, 3, null, 'GHI789');

        $items = collect();
        if ($typeId === null || $typeId === 3) {
            $items->push($storageUnit3);
        }
        $total = 11; // Total items across all pages
        $mockPaginator = new LengthAwarePaginator(
            $items,
            $total,
            $perPage,
            $page
        );

        $this->repositoryMock->shouldReceive('paginate')
            ->once()
            ->with($perPage, $page, $warehouseId, $typeId)
            ->andReturn($mockPaginator);

        $result = $this->service->paginateStorageUnits($perPage, $page, $warehouseId, $typeId);

        $this->assertInstanceOf(LengthAwarePaginator::class, $result);
        if ($typeId === null || $typeId === 3) {
            $this->assertEquals(1, $result->count());
        } else {
            $this->assertEquals(0, $result->count());
        }
        $this->assertEquals($total, $result->total());
        $this->assertEquals($page, $result->currentPage());
    }

    /**
     * 
     */
    #[DataProvider('typeIdProvider')]
    public function test_paginate_warehouses_empty_result(?int $typeId): void {
        $perPage = 10;
        $page = 1;
        $warehouseId = 1;

        $items = collect([]);
        $total = 0;
        $mockPaginator = new LengthAwarePaginator(
            $items,
            $total,
            $perPage,
            $page
        );

        $this->repositoryMock->shouldReceive('paginate')
            ->once()
            ->with($perPage, $page, $warehouseId, $typeId)
            ->andReturn($mockPaginator);

        $result = $this->service->paginateStorageUnits($perPage, $page, $warehouseId, $typeId);

        $this->assertInstanceOf(LengthAwarePaginator::class, $result);
        $this->assertEquals(0, $result->count());
        $this->assertEquals(0, $result->total());
    }

    /**
     * #[DataProvider('typeIdProvider')]
     */
    public static function typeIdProvider(): array {
        return [
            'with null type_id' => [null],
            'with type_id 1' => [1],
            'with type_id 2' => [2],
            'with type_id 3' => [3],
        ];
    }
}
