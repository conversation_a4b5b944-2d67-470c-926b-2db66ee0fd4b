<?php

namespace Tests\Unit\WarehouseManagement\Interfaces\Http\Controllers;

use Tests\TestCase;
use App\WarehouseManagement\Interfaces\Http\Controllers\StorageUnitAPIController;
use App\WarehouseManagement\Application\Services\StorageUnitService;
use App\WarehouseManagement\Domain\Entities\StorageUnit;
use App\WarehouseManagement\Interfaces\Http\Requests\CreateStorageUnitRequest;
use Mockery;
use Illuminate\Http\JsonResponse;
use PHPUnit\Framework\Attributes\DataProvider;
use Illuminate\Validation\ValidationException;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Support\MessageBag;
use App\WarehouseManagement\Application\DTOs\StorageUnitDTO;
use App\Settings\Domain\Entities\StorageUnitType;
use App\Settings\Domain\ValueObjects\StorageUnitTypeCode;
use App\WarehouseManagement\Interfaces\Http\Requests\ListStorageUnitsRequest;
use Illuminate\Pagination\LengthAwarePaginator;

class StorageUnitAPIControllerTest extends TestCase {
    private $storageUnitServiceMock;
    private $controller;

    private const TEST_WAREHOUSE_ID = '978d3b8d';
    private const TEST_STORAGE_UNIT_ID_1 = '00000001';
    private const TEST_STORAGE_UNIT_ID_2 = '00000002';
    private const TEST_STORAGE_UNIT_ID_3 = '00000003';

    protected function setUp(): void {
        parent::setUp();
        $this->storageUnitServiceMock = Mockery::mock(StorageUnitService::class);
        $this->controller = new StorageUnitAPIController($this->storageUnitServiceMock);
    }

    protected function tearDown(): void {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test creating storage units
     */
    #[DataProvider('storageUnitCreationDataProvider')]
    public function test_create_storage_units(array $requestData, array $expectedUnits): void {
        // Arrange
        $warehouseId = self::TEST_WAREHOUSE_ID;

        // Data with warehouse_id that will be passed to service
        $serviceData = array_merge($requestData, ['warehouse_id' => $warehouseId]);

        // Mock the request
        $requestMock = Mockery::mock(CreateStorageUnitRequest::class);
        $requestMock->shouldReceive('validated')
            ->once()
            ->andReturn($serviceData);

        // Mock the service
        $this->storageUnitServiceMock->shouldReceive('createStorageUnit')
            ->once()
            ->with($serviceData)
            ->andReturn($expectedUnits);

        // Act
        $response = $this->controller->store($requestMock, $warehouseId);

        // Assert using direct assertions
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(201, $response->getStatusCode());

        // Assert the structure and values
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('Storage units created successfully', $responseData['message']);
        $this->assertCount(count($expectedUnits), $responseData['data']);

        foreach ($expectedUnits as $index => $unit) {
            $this->assertEquals($unit->storageUnit->getWarehouseId(), $responseData['data'][$index]['warehouse_id']);
            $this->assertEquals($unit->storageUnit->getTypeId(), $responseData['data'][$index]['type_id']);
            $this->assertEquals($unit->type->getName(), $responseData['data'][$index]['name']);
        }
    }

    public static function storageUnitCreationDataProvider(): array {
        return [
            'single storage unit' => [
                [
                    'storageUnits' => [
                        [
                            'quantity' => 1,
                            'type_id' => 2
                        ]
                    ]
                ],
                [
                    self::createStorageUnitDTO(1, 2, self::TEST_STORAGE_UNIT_ID_1, 'Box Type 2')
                ]
            ],
            'multiple storage units' => [
                [
                    'storageUnits' => [
                        [
                            'quantity' => 2,
                            'type_id' => 2
                        ],
                        [
                            'quantity' => 1,
                            'type_id' => 3
                        ]
                    ]
                ],
                [
                    self::createStorageUnitDTO(1, 2, self::TEST_STORAGE_UNIT_ID_1, 'Box Type 2'),
                    self::createStorageUnitDTO(1, 2, self::TEST_STORAGE_UNIT_ID_2, 'Box Type 2'),
                    self::createStorageUnitDTO(1, 3, self::TEST_STORAGE_UNIT_ID_3, 'Pallet Type 3')
                ]
            ]
        ];
    }

    /**
     * Helper method to create a StorageUnitDTO for testing
     */
    private static function createStorageUnitDTO(
        string $warehouseId,
        int $typeId,
        string $id,
        string $typeName = 'Test Type'
    ): StorageUnitDTO {
        // Create the storage unit entity
        $storageUnit = new StorageUnit($warehouseId, $typeId, null, $id);
        $storageUnitType = new StorageUnitType($typeName, new StorageUnitTypeCode('SLF'), 10.5, 8.0, 6.5, 'test_box.jpg', $typeId);

        // Return the DTO
        return new StorageUnitDTO($storageUnit, $storageUnitType);
    }

    public function test_create_storage_units_with_service_exception(): void {
        // Arrange
        $warehouseId = 1;
        $requestData = [
            'storageUnit' => [
                [
                    'quantity' => 1,
                    'type_id' => 1
                ]
            ]
        ];

        // Mock the request
        $requestMock = Mockery::mock(CreateStorageUnitRequest::class);
        $requestMock->shouldReceive('validated')
            ->once()
            ->andReturn($requestData);

        // Mock the service to throw an exception
        $this->storageUnitServiceMock->shouldReceive('createStorageUnit')
            ->once()
            ->andThrow(new \Exception('Service error'));

        // Expect exception to be thrown
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Service error');

        // Act
        $this->controller->store($requestMock, $warehouseId);
    }

    public function test_create_storage_units_with_validation_failure(): void {
        // Arrange
        $warehouseId = self::TEST_WAREHOUSE_ID;

        $errors = new MessageBag();
        $errors->add('warehouse_id', 'The warehouse ID does not exist.');

        $validator = Mockery::mock(Validator::class);
        $validator->shouldReceive('errors')
            ->andReturn($errors);

        $exception = new ValidationException($validator);

        // Mock the request
        $requestMock = Mockery::mock(CreateStorageUnitRequest::class);
        $requestMock->shouldReceive('validated')
            ->once()
            ->andThrow($exception);

        $this->expectException(ValidationException::class);

        // Act
        $this->controller->store($requestMock, $warehouseId);
    }

        /**
     * Test index with default pagination parameters
     */
    #[DataProvider('typeIdProvider')]
    public function test_index_with_default_pagination_parameters(?int $typeId): void
    {
        // Arrange
        $requestMock = Mockery::mock(ListStorageUnitsRequest::class);
        $requestMock->shouldReceive('validated')
            ->once()
            ->andReturn(['per_page' => 15, 'page' => 1, 'warehouse_id' => 1, 'type_id' => $typeId]);

        $storageUnit1 = new StorageUnit(1, 1, null, self::TEST_STORAGE_UNIT_ID_1);
        $storageUnit2 = new StorageUnit(1, 2, null, self::TEST_STORAGE_UNIT_ID_2);

        $storageUnitDTO1 = new StorageUnitDTO(
            $storageUnit1,
            new StorageUnitType('Box Type 1', new StorageUnitTypeCode('SLF'), 10, 10, 10, 'test.jpg', 1)
        );
        $storageUnitDTO2 = new StorageUnitDTO(
            $storageUnit2,
            new StorageUnitType('Box Type 2', new StorageUnitTypeCode('VOL'), 10, 10, 10, 'test.jpg', 2)
        );

        // Create mock paginator
        $items = collect();
        if ($typeId === null || $typeId === 1) {
            $items->push($storageUnitDTO1);
        }
        if ($typeId === null || $typeId === 2) {
            $items->push($storageUnitDTO2);
        }
        $total = $items->count();
        $perPage = 15;
        $page = 1;
        
        $mockPaginator = new LengthAwarePaginator(
            $items,
            $total,
            $perPage,
            $page,
        );
        $this->storageUnitServiceMock->shouldReceive('paginateStorageUnits')
            ->once()
            ->with(15, 1, 1, $typeId)
            ->andReturn($mockPaginator);

        // Act
        $response = $this->controller->index($requestMock,1);

        // Assert
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        
        // Check response content
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('Storage units retrieved successfully', $responseData['message']);
        $this->assertCount($total, $responseData['data']);
        
        if ($typeId === null) {
            // Check the IDs match what we set in the test
            $this->assertEquals(self::TEST_STORAGE_UNIT_ID_1, $responseData['data'][0]['id']);
            $this->assertEquals(self::TEST_STORAGE_UNIT_ID_2, $responseData['data'][1]['id']);
            
            // Check warehouse_id
            $this->assertEquals(1, $responseData['data'][0]['warehouse_id']);
            $this->assertEquals(1, $responseData['data'][1]['warehouse_id']);
            
            // Check type_id
            $this->assertEquals(1, $responseData['data'][0]['type_id']);
            $this->assertEquals(2, $responseData['data'][1]['type_id']);
        } elseif ($typeId === 1) {
            $this->assertEquals(self::TEST_STORAGE_UNIT_ID_1, $responseData['data'][0]['id']);
            $this->assertEquals(1, $responseData['data'][0]['type_id']);
        } elseif ($typeId === 2) {
            $this->assertEquals(self::TEST_STORAGE_UNIT_ID_2, $responseData['data'][0]['id']);
            $this->assertEquals(2, $responseData['data'][0]['type_id']);
        }
        
        $this->assertEquals([
            "total" => $total,
            "per_page" => 15,
            "current_page" => 1,
            "last_page" => 1,
        ], $responseData["pagination"]);
    }

    public function test_index_with_empty_result(): void
    {
        $requestMock = Mockery::mock(ListStorageUnitsRequest::class);
        $requestMock->shouldReceive('validated')
            ->once()
            ->andReturn(['per_page' => 15, 'page' => 1, 'warehouse_id' => 1]);

        $items = [];
        $total = 0;
        $perPage = 15;
        $page = 1;
        $mockPaginator = new LengthAwarePaginator(
            $items,
            $total,
            $perPage,
            $page
        );
        $this->storageUnitServiceMock->shouldReceive('paginateStorageUnits')
            ->once()
            ->with(15, 1, 1, null)
            ->andReturn($mockPaginator);

        $response = $this->controller->index($requestMock,1);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        
        // Check response content
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('Storage units retrieved successfully', $responseData['message']);
        $this->assertCount(0, $responseData['data']);
        $this->assertEquals([
            "total" => 0,
            "per_page" => 15,
            "current_page" => 1,
            "last_page" => 1,
        ], $responseData["pagination"]);

    }

    /**
     * Test index with different page
     */
    #[DataProvider('typeIdProvider')]
    public static function typeIdProvider(): array
    {
        return [
            'with null type_id' => [null],
            'with type_id 1' => [1],
            'with type_id 2' => [2],
            'with type_id 3' => [3],
        ];
    }
}
