<?php

namespace Tests\Unit\Settings\Application\Services;

use Tests\TestCase;
use App\Settings\Application\Services\MaterialService;
use App\Settings\Domain\Repositories\MaterialRepositoryInterface;
use App\Settings\Domain\Entities\Material;
use Mockery;
use App\Core\Utils\IdGenerator;
use DomainException;

class MaterialServiceTest extends TestCase {
    private MaterialService $materialService;
    private Mockery\MockInterface&MaterialRepositoryInterface $materialRepositoryMock;

    private const TEST_MATERIAL_ID_1 = 'mat00001';
    private const TEST_MATERIAL_ID_2 = 'mat00002';

    protected function setUp(): void {
        parent::setUp();

        $this->materialRepositoryMock = Mockery::mock(MaterialRepositoryInterface::class);
        $this->materialService = new MaterialService($this->materialRepositoryMock);
    }

    protected function tearDown(): void {
        Mockery::close();
        parent::tearDown();
    }

    public function test_create_material_successfully(): void {
        $materialData = [
            'name' => 'Test Material',
            'threshold' => 10,
            'stock_quantity' => 20,
        ];
        $expectedId = self::TEST_MATERIAL_ID_1;

        $this->mockIdGenerator($expectedId);

        $this->materialRepositoryMock->shouldReceive('exists')
            ->andReturn(false);

        $this->materialRepositoryMock->shouldReceive('create')
            ->once()
            ->andReturnUsing(function ($materialParam) {
                return new Material(
                    $materialParam->getName(),
                    $materialParam->getThreshold(),
                    $materialParam->getStockQuantity(),
                    $materialParam->getId()
                );
            });

        $createdMaterial = $this->materialService->createMaterial($materialData);

        $this->assertInstanceOf(Material::class, $createdMaterial);
        $this->assertEquals($materialData['name'], $createdMaterial->getName());
        $this->assertEquals($materialData['threshold'], $createdMaterial->getThreshold());
        $this->assertEquals($materialData['stock_quantity'], $createdMaterial->getStockQuantity());
        $this->assertEquals($expectedId, $createdMaterial->getId());
    }

    public function test_create_material_with_invalid_data_throws_exception(): void {
        $invalidMaterialData = [
            'name' => 'Test Material',
            'threshold' => -10,
            'stock_quantity' => 20,
        ];
        $expectedId = self::TEST_MATERIAL_ID_1;

        $this->mockIdGenerator($expectedId);
        $this->materialRepositoryMock->shouldReceive('exists')
            ->andReturn(false);

        $this->expectException(DomainException::class);
        $this->expectExceptionMessage('Threshold must be greater than zero');

        $this->materialService->createMaterial($invalidMaterialData);
    }

    public function test_update_material_successfully(): void {
        // Arrange
        $materialId = self::TEST_MATERIAL_ID_1;
        $updateData = [
            'name' => 'Updated Material Name',
            'threshold' => 15,
            'stock_quantity' => 30,
        ];

        $updatedMaterial = new Material(
            $updateData['name'],
            $updateData['threshold'],
            $updateData['stock_quantity'],
            $materialId
        );

        $this->materialRepositoryMock->shouldReceive('update')
            ->once()
            ->with($materialId, $updateData)
            ->andReturn($updatedMaterial);

        // Act
        $result = $this->materialService->updateMaterial($materialId, $updateData);

        // Assert
        $this->assertInstanceOf(Material::class, $result);
        $this->assertEquals($updateData['name'], $result->getName());
        $this->assertEquals($updateData['threshold'], $result->getThreshold());
        $this->assertEquals($updateData['stock_quantity'], $result->getStockQuantity());
        $this->assertEquals($materialId, $result->getId());
    }

    public function test_update_material_not_found_returns_null(): void {
        // Arrange
        $materialId = 'nonexistent';
        $updateData = [
            'name' => 'Updated Material Name',
            'threshold' => 15,
            'stock_quantity' => 30,
        ];

        $this->materialRepositoryMock->shouldReceive('update')
            ->once()
            ->with($materialId, $updateData)
            ->andReturn(null);

        // Act
        $result = $this->materialService->updateMaterial($materialId, $updateData);

        // Assert
        $this->assertNull($result);
    }

    public function test_delete_material_successfully(): void {
        // Arrange
        $materialId = self::TEST_MATERIAL_ID_1;
        $deletedMaterial = new Material('Test Material', 10, 20, $materialId);

        $this->materialRepositoryMock->shouldReceive('delete')
            ->once()
            ->with($materialId)
            ->andReturn($deletedMaterial);

        // Act
        $result = $this->materialService->deleteMaterial($materialId);

        // Assert
        $this->assertInstanceOf(Material::class, $result);
        $this->assertEquals($materialId, $result->getId());
        $this->assertEquals('Test Material', $result->getName());
    }

    public function test_delete_material_not_found_returns_null(): void {
        // Arrange
        $materialId = 'nonexistent';

        $this->materialRepositoryMock->shouldReceive('delete')
            ->once()
            ->with($materialId)
            ->andReturn(null);

        // Act
        $result = $this->materialService->deleteMaterial($materialId);

        // Assert
        $this->assertNull($result);
    }

    public function test_get_material_by_id_successfully(): void {
        // Arrange
        $materialId = self::TEST_MATERIAL_ID_1;
        $expectedMaterial = new Material(
            'Test Material',
            10,
            20,
            $materialId
        );

        $this->materialRepositoryMock->shouldReceive('findById')
            ->once()
            ->with($materialId)
            ->andReturn($expectedMaterial);

        // Act
        $result = $this->materialService->getMaterial($materialId);

        // Assert
        $this->assertInstanceOf(Material::class, $result);
        $this->assertEquals($expectedMaterial->getName(), $result->getName());
        $this->assertEquals($expectedMaterial->getThreshold(), $result->getThreshold());
        $this->assertEquals($expectedMaterial->getStockQuantity(), $result->getStockQuantity());
        $this->assertEquals($expectedMaterial->getId(), $result->getId());
    }

    public function test_get_material_by_id_not_found_returns_null(): void {
        // Arrange
        $materialId = 'nonexistent';

        $this->materialRepositoryMock->shouldReceive('findById')
            ->once()
            ->with($materialId)
            ->andReturn(null);

        // Act
        $result = $this->materialService->getMaterial($materialId);

        // Assert
        $this->assertNull($result);
    }

    public function test_get_all_materials_successfully(): void {
        // Arrange
        $material1 = new Material('Material 1', 10, 20, self::TEST_MATERIAL_ID_1);
        $material2 = new Material('Material 2', 15, 30, self::TEST_MATERIAL_ID_2);
        $expectedMaterials = [$material1, $material2];

        $this->materialRepositoryMock->shouldReceive('all')
            ->once()
            ->andReturn($expectedMaterials);

        // Act
        $result = $this->materialService->getAllMaterials();

        // Assert
        $this->assertIsArray($result);
        $this->assertCount(2, $result);
        $this->assertContainsOnlyInstancesOf(Material::class, $result);

        // Verify first material
        $this->assertEquals('Material 1', $result[0]->getName());
        $this->assertEquals(10, $result[0]->getThreshold());
        $this->assertEquals(20, $result[0]->getStockQuantity());
        $this->assertEquals(self::TEST_MATERIAL_ID_1, $result[0]->getId());

        // Verify second material
        $this->assertEquals('Material 2', $result[1]->getName());
        $this->assertEquals(15, $result[1]->getThreshold());
        $this->assertEquals(30, $result[1]->getStockQuantity());
        $this->assertEquals(self::TEST_MATERIAL_ID_2, $result[1]->getId());
    }

    public function test_get_all_materials_returns_empty_array_when_no_materials(): void {
        // Arrange
        $this->materialRepositoryMock->shouldReceive('all')
            ->once()
            ->andReturn([]);

        // Act
        $result = $this->materialService->getAllMaterials();

        // Assert
        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /**
     * Helper method to mock the IdGenerator
     */
    private function mockIdGenerator(string $id): void {
        Mockery::mock('alias:' . IdGenerator::class)
            ->shouldReceive('generateUnique')
            ->andReturn($id);
    }
}
