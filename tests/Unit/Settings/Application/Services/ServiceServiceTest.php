<?php

namespace Tests\Unit\Settings\Application\Services;

use Tests\TestCase;
use App\Settings\Application\Services\ServiceService;
use App\Settings\Domain\Repositories\ServiceRepositoryInterface;
use App\Settings\Domain\Entities\Service;
use Mockery;
use App\Core\Utils\IdGenerator;

class ServiceServiceTest extends TestCase {
    private ServiceService $serviceService;
    private Mockery\MockInterface&ServiceRepositoryInterface $serviceRepositoryMock;

    private const TEST_SERVICE_ID_1 = 'ser00001';
    private const TEST_SERVICE_ID_2 = 'ser00002';

    protected function setUp(): void {
        parent::setUp();

        $this->serviceRepositoryMock = Mockery::mock(ServiceRepositoryInterface::class);
        $this->serviceService = new ServiceService($this->serviceRepositoryMock);
    }

    protected function tearDown(): void {
        Mockery::close();
        parent::tearDown();
    }

    public function test_create_service_successfully(): void {
        $serviceData = [
            'name' => 'Test Service',
            'description' => 'Test Description',
        ];
        $expectedId = self::TEST_SERVICE_ID_1;

        $this->mockIdGenerator($expectedId);

        $this->serviceRepositoryMock->shouldReceive('exists')
            ->andReturn(false);

        $this->serviceRepositoryMock->shouldReceive('create')
            ->once()
            ->andReturnUsing(function ($serviceParam) {
                return new Service(
                    $serviceParam->getName(),
                    $serviceParam->getDescription(),
                    $serviceParam->getId()
                );
            });

        $createdService = $this->serviceService->createService($serviceData);

        $this->assertInstanceOf(Service::class, $createdService);
        $this->assertEquals($serviceData['name'], $createdService->getName());
        $this->assertEquals($serviceData['description'], $createdService->getDescription());
        $this->assertEquals($expectedId, $createdService->getId());
    }

    public function test_update_service_successfully(): void {
        // Arrange
        $serviceId = self::TEST_SERVICE_ID_1;
        $updateData = [
            'name' => 'Updated Service Name',
            'description' => 'Updated Description',
        ];

        $updatedService = new Service(
            $updateData['name'],
            $updateData['description'],
            $serviceId
        );

        $this->serviceRepositoryMock->shouldReceive('update')
            ->once()
            ->with($serviceId, $updateData)
            ->andReturn($updatedService);

        // Act
        $result = $this->serviceService->updateService($serviceId, $updateData);

        // Assert
        $this->assertInstanceOf(Service::class, $result);
        $this->assertEquals($updateData['name'], $result->getName());
        $this->assertEquals($updateData['description'], $result->getDescription());
        $this->assertEquals($serviceId, $result->getId());
    }

    public function test_update_service_not_found_returns_null(): void {
        // Arrange
        $serviceId = 'nonexistent';
        $updateData = [
            'name' => 'Updated Service Name',
            'description' => 'Updated Description',
        ];

        $this->serviceRepositoryMock->shouldReceive('update')
            ->once()
            ->with($serviceId, $updateData)
            ->andReturn(null);

        // Act
        $result = $this->serviceService->updateService($serviceId, $updateData);

        // Assert
        $this->assertNull($result);
    }

    public function test_delete_service_successfully(): void {
        // Arrange
        $serviceId = self::TEST_SERVICE_ID_1;
        $deletedService = new Service('Test Service', 'Test Description', $serviceId);

        $this->serviceRepositoryMock->shouldReceive('delete')
            ->once()
            ->with($serviceId)
            ->andReturn($deletedService);

        // Act
        $result = $this->serviceService->deleteService($serviceId);

        // Assert
        $this->assertInstanceOf(Service::class, $result);
        $this->assertEquals($serviceId, $result->getId());
        $this->assertEquals('Test Service', $result->getName());
    }

    public function test_delete_service_not_found_returns_null(): void {
        // Arrange
        $serviceId = 'nonexistent';

        $this->serviceRepositoryMock->shouldReceive('delete')
            ->once()
            ->with($serviceId)
            ->andReturn(null);

        // Act
        $result = $this->serviceService->deleteService($serviceId);

        // Assert
        $this->assertNull($result);
    }

    public function test_get_service_by_id_successfully(): void {
        // Arrange
        $serviceId = self::TEST_SERVICE_ID_1;
        $expectedService = new Service(
            'Test Service',
            'Test Description',
            $serviceId
        );

        $this->serviceRepositoryMock->shouldReceive('findById')
            ->once()
            ->with($serviceId)
            ->andReturn($expectedService);

        // Act
        $result = $this->serviceService->getService($serviceId);

        // Assert
        $this->assertInstanceOf(Service::class, $result);
        $this->assertEquals($expectedService->getName(), $result->getName());
        $this->assertEquals($expectedService->getDescription(), $result->getDescription());
        $this->assertEquals($expectedService->getId(), $result->getId());
    }

    public function test_get_service_by_id_not_found_returns_null(): void {
        // Arrange
        $serviceId = 'nonexistent';

        $this->serviceRepositoryMock->shouldReceive('findById')
            ->once()
            ->with($serviceId)
            ->andReturn(null);

        // Act
        $result = $this->serviceService->getService($serviceId);

        // Assert
        $this->assertNull($result);
    }

    public function test_get_all_services_successfully(): void {
        // Arrange
        $service1 = new Service('Service 1', 'Test Description 1', self::TEST_SERVICE_ID_1);
        $service2 = new Service('Service 2', 'Test Description 2', self::TEST_SERVICE_ID_2);
        $expectedServices = [$service1, $service2];

        $this->serviceRepositoryMock->shouldReceive('all')
            ->once()
            ->andReturn($expectedServices);

        // Act
        $result = $this->serviceService->getAllServices();

        // Assert
        $this->assertIsArray($result);
        $this->assertCount(2, $result);
        $this->assertContainsOnlyInstancesOf(Service::class, $result);

        // Verify first service
        $this->assertEquals('Service 1', $result[0]->getName());
        $this->assertEquals('Test Description 1', $result[0]->getDescription());
        $this->assertEquals(self::TEST_SERVICE_ID_1, $result[0]->getId());

        // Verify second service
        $this->assertEquals('Service 2', $result[1]->getName());
        $this->assertEquals('Test Description 2', $result[1]->getDescription());
        $this->assertEquals(self::TEST_SERVICE_ID_2, $result[1]->getId());
    }

    public function test_get_all_services_returns_empty_array_when_no_services(): void {
        // Arrange
        $this->serviceRepositoryMock->shouldReceive('all')
            ->once()
            ->andReturn([]);

        // Act
        $result = $this->serviceService->getAllServices();

        // Assert
        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /**
     * Helper method to mock the IdGenerator
     */
    private function mockIdGenerator(string $id): void {
        Mockery::mock('alias:' . IdGenerator::class)
            ->shouldReceive('generateUnique')
            ->andReturn($id);
    }
}
