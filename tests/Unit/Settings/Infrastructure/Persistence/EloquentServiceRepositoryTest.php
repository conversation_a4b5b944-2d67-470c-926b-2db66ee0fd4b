<?php

namespace Tests\Unit\Settings\Infrastructure\Persistence;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Settings\Infrastructure\Persistence\EloquentServiceRepository;
use App\Settings\Infrastructure\Models\Service as ServiceEloquent;
use App\Settings\Domain\Entities\Service;
use Illuminate\Database\QueryException;

class EloquentServiceRepositoryTest extends TestCase {
    use RefreshDatabase;

    private EloquentServiceRepository $repository;
    private const TEST_SERVICE_ID_1 = 'ser00001';
    private const TEST_SERVICE_ID_2 = 'ser00002';

    protected function setUp(): void {
        parent::setUp();
        $this->repository = new EloquentServiceRepository();
    }

    public function test_create_service_successfully(): void {
        $service = new Service('Test Service', 'Test Description', self::TEST_SERVICE_ID_1);
        $createdService = $this->repository->create($service);

        $this->assertInstanceOf(Service::class, $createdService);
        $this->assertEquals($service->getName(), $createdService->getName());
        $this->assertEquals($service->getDescription(), $createdService->getDescription());
        $this->assertEquals($service->getId(), $createdService->getId());
    }

    public function test_create_service_with_duplicate_id_throws_exception(): void {
        $service1 = new Service('Test Service 1', 'Test Description', self::TEST_SERVICE_ID_1);
        $this->repository->create($service1);

        $service2 = new Service('Test Service 2', 'Test Description', self::TEST_SERVICE_ID_1);

        $this->expectException(QueryException::class);
        $this->expectExceptionMessageMatches('/unique|duplicate|constraint/i');
        $this->repository->create($service2);
    }

    public function test_find_service_by_id_successfully(): void {
        $service = new Service('Test Service', 'Test Description', self::TEST_SERVICE_ID_1);
        $this->repository->create($service);

        $foundService = $this->repository->findById(self::TEST_SERVICE_ID_1);

        $this->assertInstanceOf(Service::class, $foundService);
        $this->assertEquals($service->getName(), $foundService->getName());
        $this->assertEquals($service->getDescription(), $foundService->getDescription());
        $this->assertEquals($service->getId(), $foundService->getId());
    }

    public function test_find_service_by_id_not_found_returns_null(): void {
        $foundService = $this->repository->findById('nonexistent');
        $this->assertNull($foundService);
    }

    public function test_update_service_successfully(): void {
        $service = new Service('Test Service', 'Test Description', self::TEST_SERVICE_ID_1);
        $this->repository->create($service);

        $updateData = [
            'name' => 'Updated Service Name',
            'description' => 'Updated Description',
        ];

        $updatedService = $this->repository->update(self::TEST_SERVICE_ID_1, $updateData);

        $this->assertInstanceOf(Service::class, $updatedService);
        $this->assertEquals($updateData['name'], $updatedService->getName());
        $this->assertEquals($updateData['description'], $updatedService->getDescription());
        $this->assertEquals(self::TEST_SERVICE_ID_1, $updatedService->getId());
    }

    public function test_update_service_not_found_returns_null(): void {
        $updatedService = $this->repository->update('nonexistent', [
            'name' => 'Updated Service Name',
            'description' => 'Updated Description',
        ]);

        $this->assertNull($updatedService);
    }

    public function test_delete_service_successfully(): void {
        $service = new Service('Test Service', 'Test Description', self::TEST_SERVICE_ID_1);
        $this->repository->create($service);

        $deleted = $this->repository->delete(self::TEST_SERVICE_ID_1);
        $this->assertInstanceOf(Service::class, $deleted);
        $this->assertEquals(self::TEST_SERVICE_ID_1, $deleted->getId());
    }

    public function test_delete_service_not_found_returns_null(): void {
        $deleted = $this->repository->delete('nonexistent');
        $this->assertNull($deleted);
    }

    public function test_exists_service_returns_true_when_exists(): void {
        $service = new Service('Test Service', 'Test Description', self::TEST_SERVICE_ID_1);
        $this->repository->create($service);

        $exists = $this->repository->exists(self::TEST_SERVICE_ID_1);
        $this->assertTrue($exists);
    }

    public function test_exists_service_returns_false_when_not_exists(): void {
        $exists = $this->repository->exists('nonexistent');
        $this->assertFalse($exists);
    }

    public function test_get_all_services_successfully(): void {
        $service1 = new Service('Service 1', 'Test Description 1', self::TEST_SERVICE_ID_1);
        $service2 = new Service('Service 2', 'Test Description 2', self::TEST_SERVICE_ID_2);
        $this->repository->create($service1);
        $this->repository->create($service2);

        $services = $this->repository->all();
        $this->assertIsArray($services);
        $this->assertCount(2, $services);
        $this->assertContainsOnlyInstancesOf(Service::class, $services);
        $this->assertEquals($service1->getName(), $services[0]->getName());
        $this->assertEquals($service1->getDescription(), $services[0]->getDescription());
        $this->assertEquals($service1->getId(), $services[0]->getId());
        $this->assertEquals($service2->getName(), $services[1]->getName());
        $this->assertEquals($service2->getDescription(), $services[1]->getDescription());
        $this->assertEquals($service2->getId(), $services[1]->getId());
    }

    public function test_get_all_services_returns_empty_array_when_no_services(): void {
        $services = $this->repository->all();
        $this->assertIsArray($services);
        $this->assertEmpty($services);
    }
}
