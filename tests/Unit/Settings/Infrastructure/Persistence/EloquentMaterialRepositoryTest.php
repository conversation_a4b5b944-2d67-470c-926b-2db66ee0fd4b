<?php

namespace Tests\Unit\Settings\Infrastructure\Persistence;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Settings\Infrastructure\Persistence\EloquentMaterialRepository;
use App\Settings\Infrastructure\Models\Material as MaterialEloquent;
use App\Settings\Domain\Entities\Material;
use Illuminate\Database\QueryException;

class EloquentMaterialRepositoryTest extends TestCase {
    use RefreshDatabase;

    private EloquentMaterialRepository $repository;
    private const TEST_MATERIAL_ID_1 = 'mat00001';
    private const TEST_MATERIAL_ID_2 = 'mat00002';

    protected function setUp(): void {
        parent::setUp();
        $this->repository = new EloquentMaterialRepository();
    }

    public function test_create_material_successfully(): void {
        $material = new Material('Test Material', 10, 20, self::TEST_MATERIAL_ID_1);
        $createdMaterial = $this->repository->create($material);

        $this->assertInstanceOf(Material::class, $createdMaterial);
        $this->assertEquals($material->getName(), $createdMaterial->getName());
        $this->assertEquals($material->getThreshold(), $createdMaterial->getThreshold());
        $this->assertEquals($material->getStockQuantity(), $createdMaterial->getStockQuantity());
        $this->assertEquals($material->getId(), $createdMaterial->getId());
    }

    public function test_create_material_with_duplicate_id_throws_exception(): void {
        $material1 = new Material('Test Material 1', 10, 20, self::TEST_MATERIAL_ID_1);
        $this->repository->create($material1);

        $material2 = new Material('Test Material 2', 15, 30, self::TEST_MATERIAL_ID_1);

        $this->expectException(QueryException::class);
        $this->expectExceptionMessageMatches('/unique|duplicate|constraint/i');
        $this->repository->create($material2);
    }

    public function test_find_material_by_id_successfully(): void {
        $material = new Material('Test Material', 10, 20, self::TEST_MATERIAL_ID_1);
        $this->repository->create($material);

        $foundMaterial = $this->repository->findById(self::TEST_MATERIAL_ID_1);

        $this->assertInstanceOf(Material::class, $foundMaterial);
        $this->assertEquals($material->getName(), $foundMaterial->getName());
        $this->assertEquals($material->getThreshold(), $foundMaterial->getThreshold());
        $this->assertEquals($material->getStockQuantity(), $foundMaterial->getStockQuantity());
        $this->assertEquals($material->getId(), $foundMaterial->getId());
    }

    public function test_find_material_by_id_not_found_returns_null(): void {
        $foundMaterial = $this->repository->findById('nonexistent');
        $this->assertNull($foundMaterial);
    }

    public function test_update_material_successfully(): void {
        $material = new Material('Test Material', 10, 20, self::TEST_MATERIAL_ID_1);
        $this->repository->create($material);

        $updateData = [
            'name' => 'Updated Material Name',
            'threshold' => 15,
            'stock_quantity' => 30,
        ];

        $updatedMaterial = $this->repository->update(self::TEST_MATERIAL_ID_1, $updateData);

        $this->assertInstanceOf(Material::class, $updatedMaterial);
        $this->assertEquals($updateData['name'], $updatedMaterial->getName());
        $this->assertEquals($updateData['threshold'], $updatedMaterial->getThreshold());
        $this->assertEquals($updateData['stock_quantity'], $updatedMaterial->getStockQuantity());
        $this->assertEquals(self::TEST_MATERIAL_ID_1, $updatedMaterial->getId());
    }

    public function test_update_material_not_found_returns_null(): void {
        $updatedMaterial = $this->repository->update('nonexistent', [
            'name' => 'Updated Material Name',
            'threshold' => 15,
            'stock_quantity' => 30,
        ]);

        $this->assertNull($updatedMaterial);
    }

    public function test_delete_material_successfully(): void {
        $material = new Material('Test Material', 10, 20, self::TEST_MATERIAL_ID_1);
        $this->repository->create($material);

        $deleted = $this->repository->delete(self::TEST_MATERIAL_ID_1);
        $this->assertInstanceOf(Material::class, $deleted);
        $this->assertEquals(self::TEST_MATERIAL_ID_1, $deleted->getId());
    }

    public function test_delete_material_not_found_returns_false(): void {
        $deleted = $this->repository->delete('nonexistent');
        $this->assertNull($deleted);
    }

    public function test_exists_material_returns_true_when_exists(): void {
        $material = new Material('Test Material', 10, 20, self::TEST_MATERIAL_ID_1);
        $this->repository->create($material);

        $exists = $this->repository->exists(self::TEST_MATERIAL_ID_1);
        $this->assertTrue($exists);
    }

    public function test_exists_material_returns_false_when_not_exists(): void {
        $exists = $this->repository->exists('nonexistent');
        $this->assertFalse($exists);
    }

    public function test_get_all_materials_successfully(): void {
        $material1 = new Material('Material 1', 10, 20, self::TEST_MATERIAL_ID_1);
        $material2 = new Material('Material 2', 15, 30, self::TEST_MATERIAL_ID_2);
        $this->repository->create($material1);
        $this->repository->create($material2);

        $materials = $this->repository->all();
        $this->assertIsArray($materials);
        $this->assertCount(2, $materials);
        $this->assertContainsOnlyInstancesOf(Material::class, $materials);
        $this->assertEquals($material1->getName(), $materials[0]->getName());
        $this->assertEquals($material1->getThreshold(), $materials[0]->getThreshold());
        $this->assertEquals($material1->getStockQuantity(), $materials[0]->getStockQuantity());
        $this->assertEquals($material1->getId(), $materials[0]->getId());
        $this->assertEquals($material2->getName(), $materials[1]->getName());
        $this->assertEquals($material2->getThreshold(), $materials[1]->getThreshold());
        $this->assertEquals($material2->getStockQuantity(), $materials[1]->getStockQuantity());
        $this->assertEquals($material2->getId(), $materials[1]->getId());
    }

    public function test_get_all_materials_returns_empty_array_when_no_materials(): void {
        $materials = $this->repository->all();
        $this->assertIsArray($materials);
        $this->assertEmpty($materials);
    }
}
