<?php

namespace Tests\Unit\Settings;

use Tests\TestCase;
use App\Settings\Infrastructure\Models\StorageUnitType;
use App\Settings\Application\Services\StorageUnitTypeService;
use App\Settings\Domain\ValueObjects\StorageUnitTypeCode;
use App\Settings\Domain\Repositories\StorageUnitTypeRepositoryInterface;
use App\WarehouseManagement\Domain\Repositories\StorageUnitRepositoryInterface;
use App\Exceptions\BusinessRuleViolationException;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;

class StorageUnitTypeTest extends TestCase
{
    use RefreshDatabase;

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    // ========================================
    // Service Layer Tests
    // ========================================

    public function test_storage_unit_type_service_instantiation(): void
    {
        $service = app(StorageUnitTypeService::class);
        $this->assertInstanceOf(StorageUnitTypeService::class, $service);
    }

    // ========================================
    // Model/Entity Behavior Tests
    // ========================================

    public function test_storage_unit_type_can_be_created_with_valid_data(): void
    {
        $storageUnitType = StorageUnitType::create([
            'id' => 'test123',
            'name' => 'Test Storage Unit',
            'code' => StorageUnitTypeCode::SHELF,
            'length' => 10.5,
            'width' => 8.0,
            'height' => 6.5,
            'image' => 'test_image.jpg'
        ]);

        $this->assertInstanceOf(StorageUnitType::class, $storageUnitType);
        $this->assertEquals('test123', $storageUnitType->id);
        $this->assertEquals('Test Storage Unit', $storageUnitType->name);
        $this->assertEquals(StorageUnitTypeCode::SHELF, $storageUnitType->code);
        $this->assertEquals(10.5, $storageUnitType->length);
        $this->assertEquals(8.0, $storageUnitType->width);
        $this->assertEquals(6.5, $storageUnitType->height);
        $this->assertEquals('test_image.jpg', $storageUnitType->image);
    }

    public function test_storage_unit_type_uses_soft_deletes(): void
    {
        $storageUnitType = StorageUnitType::create([
            'id' => 'test456',
            'name' => 'Test Storage Unit for Deletion',
            'code' => StorageUnitTypeCode::VOLUME,
            'length' => 15.0,
            'width' => 12.0,
            'height' => 8.0,
            'image' => 'delete_test.jpg'
        ]);

        // Delete the storage unit type
        $storageUnitType->delete();

        // Verify it's soft deleted
        $this->assertSoftDeleted('storage_unit_types', [
            'id' => 'test456'
        ]);

        // Verify it's not returned in normal queries
        $this->assertNull(StorageUnitType::find('test456'));

        // Verify it can be found with trashed
        $this->assertNotNull(StorageUnitType::withTrashed()->find('test456'));
    }

    // ========================================
    // Value Object Tests
    // ========================================

    public function test_storage_unit_type_code_constants_are_defined(): void
    {
        $this->assertEquals('SLF', StorageUnitTypeCode::SHELF);
        $this->assertEquals('VOL', StorageUnitTypeCode::VOLUME);
        $this->assertEquals('FLR', StorageUnitTypeCode::FLOOR);
        $this->assertEquals('SPL', StorageUnitTypeCode::SPECIAL);
    }

    public function test_storage_unit_type_code_values_are_immutable(): void
    {
        // Test that the code constants return the expected values
        $validCodes = [
            StorageUnitTypeCode::SHELF,
            StorageUnitTypeCode::VOLUME,
            StorageUnitTypeCode::FLOOR,
            StorageUnitTypeCode::SPECIAL
        ];

        $this->assertCount(4, $validCodes);
        $this->assertContains('SLF', $validCodes);
        $this->assertContains('VOL', $validCodes);
        $this->assertContains('FLR', $validCodes);
        $this->assertContains('SPL', $validCodes);
    }

    // ========================================
    // Business Logic Validation Tests
    // ========================================

    public function test_storage_unit_type_requires_positive_dimensions(): void
    {
        // This test verifies that the validation rules work as expected
        // The actual validation is handled by Laravel's validation rules
        
        $validData = [
            'id' => 'test789',
            'name' => 'Valid Storage Unit',
            'code' => StorageUnitTypeCode::SHELF,
            'length' => 10.0,
            'width' => 8.0,
            'height' => 6.0,
            'image' => 'valid.jpg'
        ];

        $storageUnitType = StorageUnitType::create($validData);
        $this->assertInstanceOf(StorageUnitType::class, $storageUnitType);

        // Verify all dimensions are positive
        $this->assertGreaterThan(0, $storageUnitType->length);
        $this->assertGreaterThan(0, $storageUnitType->width);
        $this->assertGreaterThan(0, $storageUnitType->height);
    }

    public function test_prevents_deletion_when_storage_units_exist(): void
    {
        // Arrange
        $storageUnitTypeRepo = Mockery::mock(StorageUnitTypeRepositoryInterface::class);
        $storageUnitRepo = Mockery::mock(StorageUnitRepositoryInterface::class);
        $service = new StorageUnitTypeService($storageUnitTypeRepo, $storageUnitRepo);

        $storageUnitTypeRepo->shouldReceive('exists')->with('type123')->andReturn(true);
        $storageUnitRepo->shouldReceive('existsByTypeId')->with('type123')->andReturn(true);

        // Assert & Act
        $this->expectException(BusinessRuleViolationException::class);
        $this->expectExceptionMessage('Cannot delete storage unit type because it is being used by existing storage units');

        $service->deleteStorageUnitType('type123');
    }

    public function test_storage_unit_type_name_uniqueness_should_be_handled_at_application_level(): void
    {
        // Create first storage unit type
        StorageUnitType::create([
            'id' => 'test001',
            'name' => 'Test Storage Unit',
            'code' => StorageUnitTypeCode::SHELF,
            'length' => 10.0,
            'width' => 8.0,
            'height' => 6.0,
            'image' => 'unique1.jpg'
        ]);

        // Verify first record was created
        $this->assertDatabaseHas('storage_unit_types', [
            'id' => 'test001',
            'name' => 'Test Storage Unit'
        ]);

        // Create another with the same name (this shows uniqueness should be enforced at application level)
        $second = StorageUnitType::create([
            'id' => 'test002',
            'name' => 'Test Storage Unit', // Same name
            'code' => StorageUnitTypeCode::VOLUME,
            'length' => 15.0,
            'width' => 12.0,
            'height' => 8.0,
            'image' => 'unique2.jpg'
        ]);

        // Both records should exist (uniqueness enforcement is at application level, not database level)
        $this->assertDatabaseHas('storage_unit_types', ['id' => 'test001']);
        $this->assertDatabaseHas('storage_unit_types', ['id' => 'test002']);
        $this->assertInstanceOf(StorageUnitType::class, $second);
    }

    public function test_storage_unit_type_code_can_be_updated_at_model_level(): void
    {
        $storageUnitType = StorageUnitType::create([
            'id' => 'test999',
            'name' => 'Code Update Test',
            'code' => StorageUnitTypeCode::SHELF,
            'length' => 10.0,
            'width' => 8.0,
            'height' => 6.0,
            'image' => 'update.jpg'
        ]);

        $originalCode = $storageUnitType->code;

        // Update the code at model level (business logic immutability should be enforced at service/controller level)
        $storageUnitType->update([
            'name' => 'Updated Name',
            'length' => 20.0,
            'code' => StorageUnitTypeCode::VOLUME
        ]);

        // Refresh from database
        $storageUnitType->refresh();

        // At the model level, the code can be updated (immutability is enforced at higher levels)
        $this->assertNotEquals($originalCode, $storageUnitType->code);
        $this->assertEquals(StorageUnitTypeCode::VOLUME, $storageUnitType->code);
    }

    // ========================================
    // Database Integration Tests
    // ========================================

    public function test_storage_unit_type_can_be_persisted_and_retrieved(): void
    {
        $data = [
            'id' => 'persist1', // ID must be 8 characters or less
            'name' => 'Persistence Test Unit',
            'code' => StorageUnitTypeCode::FLOOR,
            'length' => 25.5,
            'width' => 20.0,
            'height' => 15.5,
            'image' => 'persistence.jpg'
        ];

        // Create and persist
        StorageUnitType::create($data);

        // Retrieve from database
        $retrieved = StorageUnitType::find('persist1');

        $this->assertNotNull($retrieved);
        $this->assertEquals($data['name'], $retrieved->name);
        $this->assertEquals($data['code'], $retrieved->code);
        $this->assertEquals($data['length'], $retrieved->length);
        $this->assertEquals($data['width'], $retrieved->width);
        $this->assertEquals($data['height'], $retrieved->height);
        $this->assertEquals($data['image'], $retrieved->image);
    }

    public function test_storage_unit_type_timestamps_are_managed(): void
    {
        $storageUnitType = StorageUnitType::create([
            'id' => 'time123', // ID must be 8 characters or less
            'name' => 'Timestamp Test Unit',
            'code' => StorageUnitTypeCode::SPECIAL,
            'length' => 10.0,
            'width' => 8.0,
            'height' => 6.0,
            'image' => 'timestamp.jpg'
        ]);

        // Verify timestamps are set
        $this->assertNotNull($storageUnitType->created_at);
        $this->assertNotNull($storageUnitType->updated_at);

        $originalUpdatedAt = $storageUnitType->updated_at;

        // Update the record
        sleep(1); // Ensure time difference
        $storageUnitType->update(['name' => 'Updated Timestamp Test']);

        // Verify updated_at changed
        $this->assertNotEquals($originalUpdatedAt, $storageUnitType->updated_at);
    }
}
