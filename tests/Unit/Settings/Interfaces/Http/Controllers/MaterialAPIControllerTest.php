<?php

namespace Tests\Unit\Settings\Interfaces\Http\Controllers;

use Tests\TestCase;
use App\Settings\Interfaces\Http\Controllers\MaterialAPIController;
use App\Settings\Application\Services\MaterialService;
use App\Settings\Domain\Entities\Material;
use App\Settings\Interfaces\Http\Requests\ListMaterialsRequest;
use BadMethodCallException;
use Mockery;
use Mockery\MockInterface;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use App\Settings\Interfaces\Http\Requests\CreateMaterialRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Support\MessageBag;
use Illuminate\Validation\ValidationException;
use App\Settings\Interfaces\Http\Requests\UpdateMaterialRequest;


class MaterialAPIControllerTest extends TestCase {
    private MaterialAPIController&MockInterface $controller;
    private MaterialService&MockInterface $materialServiceMock;
    private const TEST_MATERIAL_ID_1 = 'mat00001';
    private const TEST_MATERIAL_ID_2 = 'mat00002';

    protected function setUp(): void {
        parent::setUp();
        $this->materialServiceMock = Mockery::mock(MaterialService::class);
        $this->controller = Mockery::mock(MaterialAPIController::class, [$this->materialServiceMock])
            ->makePartial()
            ->shouldAllowMockingProtectedMethods();
    }

    protected function tearDown(): void {
        Mockery::close();
        parent::tearDown();
    }

    public function test_index_returns_materials_successfully(): void {
        $requestMock = Mockery::mock(ListMaterialsRequest::class);

        $material1 = self::createMaterialEntity('Material 1', 10, 20, self::TEST_MATERIAL_ID_1);
        $material2 = self::createMaterialEntity('Material 2', 15, 30, self::TEST_MATERIAL_ID_2);
        $materials = [$material1, $material2];

        $this->materialServiceMock->shouldReceive('getAllMaterials')
            ->once()
            ->andReturn($materials);

        $response = $this->controller->index($requestMock);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('Materials retrieved successfully', $responseData['message']);
        $this->assertCount(2, $responseData['data']);
    }

    public function test_index_returns_empty_collection_when_no_materials(): void {
        $requestMock = Mockery::mock(ListMaterialsRequest::class);
        $materials = [];

        $this->materialServiceMock->shouldReceive('getAllMaterials')
            ->once()
            ->andReturn($materials);

        $response = $this->controller->index($requestMock);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('Materials retrieved successfully', $responseData['message']);
        $this->assertEmpty($responseData['data']);
    }

    public function test_show_returns_material_successfully(): void {
        $material = self::createMaterialEntity('Material 1', 10, 20, 'mat00001');

        $this->controller->shouldReceive('authorize')
            ->once()
            ->with('view', Material::class)
            ->andReturn(true);

        $this->materialServiceMock->shouldReceive('getMaterial')
            ->once()
            ->with(self::TEST_MATERIAL_ID_1)
            ->andReturn($material);

        $response = $this->controller->show(self::TEST_MATERIAL_ID_1);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('Material retrieved successfully', $responseData['message']);
        $this->assertEquals($material->getId(), $responseData['data']['id']);
        $this->assertEquals($material->getName(), $responseData['data']['name']);
        $this->assertEquals($material->getThreshold(), $responseData['data']['threshold']);
        $this->assertEquals($material->getStockQuantity(), $responseData['data']['stock_quantity']);
    }

    public function test_show_throws_not_found_exception_when_material_not_exists(): void {
        $this->controller->shouldReceive('authorize')
            ->once()
            ->with('view', Material::class)
            ->andReturn(true);
        $this->materialServiceMock->shouldReceive('getMaterial')
            ->once()
            ->with(self::TEST_MATERIAL_ID_1)
            ->andReturn(null);

        $this->expectException(NotFoundHttpException::class);
        $this->expectExceptionMessage('Material not found');

        $this->controller->show(self::TEST_MATERIAL_ID_1);
    }

    function test_show_throws_unauthorized_exception_when_user_cannot_view(): void {
        $this->controller->shouldReceive('authorize')
            ->once()
            ->with('view', Material::class)
            ->andReturn(false);

        $this->expectException(BadMethodCallException::class);


        $this->controller->show(self::TEST_MATERIAL_ID_1);
    }

    public function test_store_creates_material_successfully(): void {
        $requestMock = Mockery::mock(CreateMaterialRequest::class);
        $validatedData = [
            'name' => 'Test Material',
            'threshold' => 10,
            'stock_quantity' => 20,
        ];
        $requestMock->shouldReceive('validated')
            ->once()
            ->andReturn($validatedData);

        $createdMaterial = self::createMaterialEntity('Test Material', 10, 20, self::TEST_MATERIAL_ID_1);

        $this->materialServiceMock->shouldReceive('createMaterial')
            ->once()
            ->with($validatedData)
            ->andReturn($createdMaterial);

        $response = $this->controller->store($requestMock);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(201, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true); 
        $this->assertEquals('Material created successfully', $responseData['message']);
        $this->assertEquals($createdMaterial->getId(), $responseData['data']['id']);
        $this->assertEquals($createdMaterial->getName(), $responseData['data']['name']);
        $this->assertEquals($createdMaterial->getThreshold(), $responseData['data']['threshold']);
        $this->assertEquals($createdMaterial->getStockQuantity(), $responseData['data']['stock_quantity']);
    }

    public function test_store_with_validation_failure_throws_exception(): void {
        $errors = new MessageBag();
        $errors->add('name', 'The name field is required.');

        $validator = Mockery::mock(Validator::class);
        $validator->shouldReceive('errors')
            ->andReturn($errors);
        $exception = new ValidationException($validator);

        $requestMock = Mockery::mock(CreateMaterialRequest::class);
        $requestMock->shouldReceive('validated')
            ->once()
            ->andThrow($exception);

        $this->expectException(ValidationException::class);

        // Act
        $this->controller->store($requestMock);
    }

    public function test_store_with_service_exception_throws_exception(): void {
        $requestMock = Mockery::mock(CreateMaterialRequest::class);
        $validatedData = [
            'name' => 'Test Material',
            'threshold' => 10,
            'stock_quantity' => 20,
        ];
        $requestMock->shouldReceive('validated')
            ->once()
            ->andReturn($validatedData);

        $this->materialServiceMock->shouldReceive('createMaterial')
            ->once()
            ->with($validatedData)
            ->andThrow(new \Exception('Service error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Service error');

        // Act
        $this->controller->store($requestMock);
    }

    public function test_update_updates_material_successfully(): void {
        $requestMock = Mockery::mock(UpdateMaterialRequest::class);
        $validatedData = [
            'name' => 'Updated Material Name',
            'threshold' => 15,
            'stock_quantity' => 30,
        ];
        $requestMock->shouldReceive('validated')
            ->once()
            ->andReturn($validatedData);

        $updatedMaterial = self::createMaterialEntity('Updated Material Name', 15, 30, self::TEST_MATERIAL_ID_1);

        $this->materialServiceMock->shouldReceive('updateMaterial')
            ->once()
            ->with(self::TEST_MATERIAL_ID_1, $validatedData)
            ->andReturn($updatedMaterial);

        $response = $this->controller->update($requestMock, self::TEST_MATERIAL_ID_1);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
    }

    public function test_update_throws_not_found_exception_when_material_not_exists(): void {
        $requestMock = Mockery::mock(UpdateMaterialRequest::class);
        $validatedData = [
            'name' => 'Updated Material Name',
            'threshold' => 15,
            'stock_quantity' => 30,
        ];
        $requestMock->shouldReceive('validated')
            ->once()
            ->andReturn($validatedData);

        $this->materialServiceMock->shouldReceive('updateMaterial')
            ->once()
            ->with(self::TEST_MATERIAL_ID_1, $validatedData)
            ->andReturn(null);

        $this->expectException(NotFoundHttpException::class);
        $this->expectExceptionMessage('Material not found');

        // Act
        $this->controller->update($requestMock, self::TEST_MATERIAL_ID_1);
    }

    public function test_update_with_validation_failure_throws_exception(): void {
        $errors = new MessageBag();
        $errors->add('name', 'A material with this name already exists.');

        $validator = Mockery::mock(Validator::class);
        $validator->shouldReceive('errors')
            ->andReturn($errors);
        $exception = new ValidationException($validator);

        $requestMock = Mockery::mock(UpdateMaterialRequest::class);
        $requestMock->shouldReceive('validated')
            ->once()
            ->andThrow($exception);

        $this->expectException(ValidationException::class);

        // Act
        $this->controller->update($requestMock, self::TEST_MATERIAL_ID_1);
    }

    public function test_destroy_deletes_material_successfully(): void {
        $material = self::createMaterialEntity('Material 1', 10, 20, 'mat00001');

        $this->controller->shouldReceive('authorize')
            ->once()
            ->with('delete', Material::class)
            ->andReturn(true);

        $this->materialServiceMock->shouldReceive('deleteMaterial')
            ->once()
            ->with(self::TEST_MATERIAL_ID_1)
            ->andReturn($material);

        $response = $this->controller->destroy(self::TEST_MATERIAL_ID_1);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('Material deleted successfully', $responseData['message']);
    }

    public function test_destroy_throws_not_found_exception_when_material_not_exists(): void {
        $this->controller->shouldReceive('authorize')
            ->once()
            ->with('delete', Material::class)
            ->andReturn(true);

        $this->materialServiceMock->shouldReceive('deleteMaterial')
            ->once()
            ->with(self::TEST_MATERIAL_ID_1)
            ->andReturn(null);

        $this->expectException(NotFoundHttpException::class);
        $this->expectExceptionMessage('Material not found');

        // Act
        $this->controller->destroy(self::TEST_MATERIAL_ID_1);
    }

    /**
     * Helper method to create a Material entity for testing
     */
    private static function createMaterialEntity(
        string $name = 'Test Material',
        int $threshold = 10,
        int $stockQuantity = 20,
        string $id = self::TEST_MATERIAL_ID_1
    ): Material {
        return new Material($name, $threshold, $stockQuantity, $id);
    }
}
