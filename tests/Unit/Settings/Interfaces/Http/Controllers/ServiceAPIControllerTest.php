<?php

namespace Tests\Unit\Settings\Interfaces\Http\Controllers;

use Tests\TestCase;
use App\Settings\Interfaces\Http\Controllers\ServiceAPIController;
use App\Settings\Application\Services\ServiceService;
use App\Settings\Domain\Entities\Service;
use App\Settings\Interfaces\Http\Requests\ListServicesRequest;
use BadMethodCallException;
use Mockery;
use Mockery\MockInterface;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use App\Settings\Interfaces\Http\Requests\CreateServiceRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Support\MessageBag;
use Illuminate\Validation\ValidationException;
use App\Settings\Interfaces\Http\Requests\UpdateServiceRequest;


class ServiceAPIControllerTest extends TestCase {
    private ServiceAPIController&MockInterface $controller;
    private ServiceService&MockInterface $serviceServiceMock;
    private const TEST_SERVICE_ID_1 = 'ser00001';
    private const TEST_SERVICE_ID_2 = 'ser00002';

    protected function setUp(): void {
        parent::setUp();
        $this->serviceServiceMock = Mockery::mock(ServiceService::class);
        $this->controller = Mockery::mock(ServiceAPIController::class, [$this->serviceServiceMock])
            ->makePartial()
            ->shouldAllowMockingProtectedMethods();
    }

    protected function tearDown(): void {
        Mockery::close();
        parent::tearDown();
    }

    public function test_index_returns_services_successfully(): void {
        $requestMock = Mockery::mock(ListServicesRequest::class);

        $service1 = self::createServiceEntity('Service 1', 'Description 1', self::TEST_SERVICE_ID_1);
        $service2 = self::createServiceEntity('Service 2', 'Description 2', self::TEST_SERVICE_ID_2);
        $services = [$service1, $service2];

        $this->serviceServiceMock->shouldReceive('getAllServices')
            ->once()
            ->andReturn($services);

        $response = $this->controller->index($requestMock);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('Services retrieved successfully', $responseData['message']);
        $this->assertCount(2, $responseData['data']);
    }

    public function test_index_returns_empty_collection_when_no_services(): void {
        $requestMock = Mockery::mock(ListServicesRequest::class);
        $services = [];

        $this->serviceServiceMock->shouldReceive('getAllServices')
            ->once()
            ->andReturn($services);

        $response = $this->controller->index($requestMock);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('Services retrieved successfully', $responseData['message']);
        $this->assertEmpty($responseData['data']);
    }

    public function test_show_returns_service_successfully(): void {
        $service = self::createServiceEntity('Service 1', 'Description 1', self::TEST_SERVICE_ID_1);

        $this->controller->shouldReceive('authorize')
            ->once()
            ->with('view', Service::class)
            ->andReturn(true);

        $this->serviceServiceMock->shouldReceive('getService')
            ->once()
            ->with(self::TEST_SERVICE_ID_1)
            ->andReturn($service);

        $response = $this->controller->show(self::TEST_SERVICE_ID_1);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('Service retrieved successfully', $responseData['message']);
        $this->assertEquals($service->getId(), $responseData['data']['id']);
        $this->assertEquals($service->getName(), $responseData['data']['name']);
        $this->assertEquals($service->getDescription(), $responseData['data']['description']);
    }

    public function test_show_throws_not_found_exception_when_service_not_exists(): void {
        $this->controller->shouldReceive('authorize')
            ->once()
            ->with('view', Service::class)
            ->andReturn(true);
        $this->serviceServiceMock->shouldReceive('getService')
            ->once()
            ->with(self::TEST_SERVICE_ID_1)
            ->andReturn(null);

        $this->expectException(NotFoundHttpException::class);
        $this->expectExceptionMessage('Service not found');

        $this->controller->show(self::TEST_SERVICE_ID_1);
    }

    function test_show_throws_unauthorized_exception_when_user_cannot_view(): void {
        $this->controller->shouldReceive('authorize')
            ->once()
            ->with('view', Service::class)
            ->andReturn(false);

        $this->expectException(BadMethodCallException::class);


        $this->controller->show(self::TEST_SERVICE_ID_1);
    }

    public function test_store_creates_service_successfully(): void {
        $requestMock = Mockery::mock(CreateServiceRequest::class);
        $validatedData = [
            'name' => 'Test Service',
            'description' => 'Test Description',
        ];
        $requestMock->shouldReceive('validated')
            ->once()
            ->andReturn($validatedData);

        $createdService = self::createServiceEntity('Test Service', 'Test Description', self::TEST_SERVICE_ID_1);

        $this->serviceServiceMock->shouldReceive('createService')
            ->once()
            ->with($validatedData)
            ->andReturn($createdService);

        $response = $this->controller->store($requestMock);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(201, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('Service created successfully', $responseData['message']);
        $this->assertEquals($createdService->getId(), $responseData['data']['id']);
        $this->assertEquals($createdService->getName(), $responseData['data']['name']);
        $this->assertEquals($createdService->getDescription(), $responseData['data']['description']);
    }

    public function test_store_with_validation_failure_throws_exception(): void {
        $errors = new MessageBag();
        $errors->add('name', 'The name field is required.');

        $validator = Mockery::mock(Validator::class);
        $validator->shouldReceive('errors')
            ->andReturn($errors);
        $exception = new ValidationException($validator);

        $requestMock = Mockery::mock(CreateServiceRequest::class);
        $requestMock->shouldReceive('validated')
            ->once()
            ->andThrow($exception);

        $this->expectException(ValidationException::class);

        // Act
        $this->controller->store($requestMock);
    }

    public function test_store_with_service_exception_throws_exception(): void {
        $requestMock = Mockery::mock(CreateServiceRequest::class);
        $validatedData = [
            'name' => 'Test Service',
            'description' => 'Test Description',
        ];
        $requestMock->shouldReceive('validated')
            ->once()
            ->andReturn($validatedData);

        $this->serviceServiceMock->shouldReceive('createService')
            ->once()
            ->with($validatedData)
            ->andThrow(new \Exception('Service error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Service error');

        // Act
        $this->controller->store($requestMock);
    }

    public function test_update_updates_service_successfully(): void {
        $requestMock = Mockery::mock(UpdateServiceRequest::class);
        $validatedData = [
            'name' => 'Updated Service Name',
            'description' => 'Updated Description',
        ];
        $requestMock->shouldReceive('validated')
            ->once()
            ->andReturn($validatedData);

        $updatedService = self::createServiceEntity('Updated Service Name', 'Updated Description', self::TEST_SERVICE_ID_1);

        $this->serviceServiceMock->shouldReceive('updateService')
            ->once()
            ->with(self::TEST_SERVICE_ID_1, $validatedData)
            ->andReturn($updatedService);

        $response = $this->controller->update($requestMock, self::TEST_SERVICE_ID_1);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
    }

    public function test_update_throws_not_found_exception_when_service_not_exists(): void {
        $requestMock = Mockery::mock(UpdateServiceRequest::class);
        $validatedData = [
            'name' => 'Updated Service Name',
            'description' => 'Updated Description',
        ];
        $requestMock->shouldReceive('validated')
            ->once()
            ->andReturn($validatedData);

        $this->serviceServiceMock->shouldReceive('updateService')
            ->once()
            ->with(self::TEST_SERVICE_ID_1, $validatedData)
            ->andReturn(null);

        $this->expectException(NotFoundHttpException::class);
        $this->expectExceptionMessage('Service not found');

        // Act
        $this->controller->update($requestMock, self::TEST_SERVICE_ID_1);
    }

    public function test_update_with_validation_failure_throws_exception(): void {
        $errors = new MessageBag();
        $errors->add('name', 'A service with this name already exists.');

        $validator = Mockery::mock(Validator::class);
        $validator->shouldReceive('errors')
            ->andReturn($errors);
        $exception = new ValidationException($validator);

        $requestMock = Mockery::mock(UpdateServiceRequest::class);
        $requestMock->shouldReceive('validated')
            ->once()
            ->andThrow($exception);

        $this->expectException(ValidationException::class);

        // Act
        $this->controller->update($requestMock, self::TEST_SERVICE_ID_1);
    }

    public function test_destroy_deletes_service_successfully(): void {
        $service = self::createServiceEntity('Service 1', 'Description 1', self::TEST_SERVICE_ID_1);

        $this->controller->shouldReceive('authorize')
            ->once()
            ->with('delete', Service::class)
            ->andReturn(true);

        $this->serviceServiceMock->shouldReceive('deleteService')
            ->once()
            ->with(self::TEST_SERVICE_ID_1)
            ->andReturn($service);

        $response = $this->controller->destroy(self::TEST_SERVICE_ID_1);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('Service deleted successfully', $responseData['message']);
    }

    public function test_destroy_throws_not_found_exception_when_service_not_exists(): void {
        $this->controller->shouldReceive('authorize')
            ->once()
            ->with('delete', Service::class)
            ->andReturn(true);

        $this->serviceServiceMock->shouldReceive('deleteService')
            ->once()
            ->with(self::TEST_SERVICE_ID_1)
            ->andReturn(null);

        $this->expectException(NotFoundHttpException::class);
        $this->expectExceptionMessage('Service not found');

        // Act
        $this->controller->destroy(self::TEST_SERVICE_ID_1);
    }

    /**
     * Helper method to create a Service entity for testing
     */
    private static function createServiceEntity(
        string $name = 'Test Service',
        string $description = 'Test Description',
        string $id = self::TEST_SERVICE_ID_1
    ): Service {
        return new Service($name, $description, $id);
    }
}
