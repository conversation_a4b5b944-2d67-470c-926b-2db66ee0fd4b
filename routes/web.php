<?php

use Illuminate\Support\Facades\Route;

// Web routes are only used for Swagger API documentation
// Only redirect to Swagger in local environment
if (app()->environment('local')) {
    Route::redirect('/', '/api/documentation');
} else {
    // Return a 404 Not Found response to hide the existence of Swagger
    Route::get('/', function () {
        abort(404);
    });

    // Block access to Swagger documentation in non-local environments
    Route::get('/api/documentation', function () {
        abort(404);
    });

    Route::get('/docs/{any}', function () {
        abort(404);
    })->where('any', '.*');
}
