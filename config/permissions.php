<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Application Permissions
    |--------------------------------------------------------------------------
    |
    | This file contains all the permissions available in the application.
    | Permissions are organized by domain/module for better maintainability.
    |
    */

    'permissions' => [

        /*
        |--------------------------------------------------------------------------
        | Warehouse Management Permissions
        |--------------------------------------------------------------------------
        */
        'warehouse' => [
            'view' => 'warehouse.view',
            'create' => 'warehouse.create',
            'update' => 'warehouse.update',
            'delete' => 'warehouse.delete',
        ],

        /*
        |--------------------------------------------------------------------------
        | Inventory Management Permissions
        |--------------------------------------------------------------------------
        */
        'inventory' => [
            'view' => 'inventory.view',
            'create' => 'inventory.create',
            'update' => 'inventory.update',
            'delete' => 'inventory.delete',
        ],

        /*
        |--------------------------------------------------------------------------
        | Settings Management Permissions
        |--------------------------------------------------------------------------
        */
        'settings' => [
            'view' => 'settings.view',
            'create' => 'settings.create',
            'update' => 'settings.update',
            'delete' => 'settings.delete',
        ],

        /*
        |--------------------------------------------------------------------------
        | Storage Unit Permissions
        |--------------------------------------------------------------------------
        */
        'storageUnit' => [
            'view' => 'storageUnit.view',
            'create' => 'storageUnit.create',
            'update' => 'storageUnit.update',
            'delete' => 'storageUnit.delete',
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Role Permission Mappings
    |--------------------------------------------------------------------------
    |
    | This section defines which permissions each role has access to.
    | Roles are defined in the Role value object.
    |
    */

    'role_permissions' => [

        /*
        |--------------------------------------------------------------------------
        | Admin Role
        |--------------------------------------------------------------------------
        | Admin has access to all features and permissions in the system.
        */
        'warehouse_admin' => [
            'warehouse.view',
            'warehouse.create',
            'warehouse.update',
            'warehouse.delete',
            'inventory.view',
            'inventory.create',
            'inventory.update',
            'inventory.delete',
            'settings.view',
            'settings.create',
            'settings.update',
            'settings.delete',
            'storageUnit.view',
            'storageUnit.create',
            'storageUnit.update',
            'storageUnit.delete',
        ],

        /*
        |--------------------------------------------------------------------------
        | Warehouse Manager Role
        |--------------------------------------------------------------------------
        | Warehouse Manager can manage their warehouse and view reports.
        | They have full inventory management capabilities and can manage settings.
        */
        'warehouse_manager' => [
            'warehouse.view',
            'warehouse.update',
            'inventory.view',
            'inventory.create',
            'inventory.update',
            'settings.view',
            'settings.create',
            'settings.update',
            'settings.delete',
            'storageUnit.view',
            'storageUnit.create',
            'storageUnit.update'
        ],

        /*
        |--------------------------------------------------------------------------
        | Stock Controller Role
        |--------------------------------------------------------------------------
        | Stock Controller focuses on inventory management.
        | They can view warehouse info but cannot modify warehouse settings.
        */
        'stock_controller' => [
            'warehouse.view',
            'inventory.view',
            'inventory.create',
            'inventory.update',
        ],

        /*
        |--------------------------------------------------------------------------
        | Fulfillment Officer Role
        |--------------------------------------------------------------------------
        | Fulfillment Officer handles order processing and fulfillment.
        | They have read-only access to warehouse and inventory data.
        */
        'fulfillment_officer' => [
            'warehouse.view',
            'inventory.view',
        ],
    ],

];
