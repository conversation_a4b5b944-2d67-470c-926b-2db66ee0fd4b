<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('storage_unit_types', function (Blueprint $table) {
            $table->string('id', 8)->primary();

            $table->string('name');
            $table->string('code'); // Storage unit type code (SLF, VOL, FLR, SPL)
            $table->float('length');
            $table->float('width');
            $table->float('height');
            $table->string('image')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->unique(['name', 'deleted_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('storage_unit_types');
    }
};
