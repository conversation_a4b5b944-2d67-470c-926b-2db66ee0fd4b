<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;

/**
 * @OA\Info(
 *     title="Wakilni WMS API Documentation",
 *     version="1.0.0",
 *     description="Warehouse Management System API Documentation",
 *     @OA\Contact(
 *         email="<EMAIL>",
 *         name="Support Team"
 *     ),
 *     @OA\License(
 *         name="Private License",
 *         url="https://wakilni.com"
 *     )
 * )
 * 
 * @OA\Server(
 *     description="Local Environment",
 *     url=L5_SWAGGER_CONST_HOST
 * )
 * 
 * @OA\SecurityScheme(
 *     securityScheme="jwt_auth",
 *     type="http",
 *     scheme="bearer",
 *     bearerFormat="JWT",
 *     description="Enter JWT token in format (Bearer <token>)"
 * )
 * 
 * @OA\Schema(
 *     schema="UnauthorizedResponse",
 *     type="object",
 *     @OA\Property(property="message", type="string", example="Authentication token was not provided"),
 *     @OA\Property(property="code", type="string", example="TOKEN_NOT_PROVIDED"),
 *     @OA\Property(property="errors", type="array", @OA\Items(type="string"), example={})
 * )
 *  @OA\Schema(
 *     schema="InternalServerErrorResponse",
 *     type="object",
 *     @OA\Property(property="message", type="string", example="An unexpected error occurred"),
 *     @OA\Property(property="code", type="string", example="INTERNAL_SERVER_ERROR"),
 *     @OA\Property(property="errors", type="array", @OA\Items(type="string"), example={})
 * )
 */
class Controller extends BaseController
{
    use AuthorizesRequests, ValidatesRequests;
}
