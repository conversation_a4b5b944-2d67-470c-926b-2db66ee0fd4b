<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class SwaggerAccessMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): mixed
    {
        // Check if the request is for Swagger documentation
        $path = $request->path();
        if (str_contains($path, 'api/documentation') || str_contains($path, 'docs')) {
            // Only allow access to Swagger in local environment
            if (app()->environment() !== 'local') {
                // Return a 404 Not Found response to hide the existence of Swagger
                abort(404);
            }
        }

        return $next($request);
    }
}
