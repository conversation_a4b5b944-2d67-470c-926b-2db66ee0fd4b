<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\ResourceCollection;

class BaseResourceCollection extends ResourceCollection
{
    /**
     * @OA\Schema(
     *     schema="PaginationResource",
     *     @OA\Property(property="current_page", type="integer", example="1"),
     *     @OA\Property(property="last_page", type="integer", example="2"),
     *     @OA\Property(property="per_page", type="integer", example=10),
     *     @OA\Property(property="total", type="integer", example="2"),
     * )
     */
    public function paginationInformation($request, $paginated, $default): array
    {
        if (!is_array($default) || !isset($default['meta'])) {
            return $default;
        }
        $default['pagination'] = [
            'current_page' => $paginated['current_page'] ?? null,
            'last_page'    => $paginated['last_page']    ?? null,
            'per_page'     => $paginated['per_page']     ?? null,
            'total'        => $paginated['total']        ?? null,
        ];

        unset($default['links']);
        unset($default['meta']);

        return $default;
    }
}
