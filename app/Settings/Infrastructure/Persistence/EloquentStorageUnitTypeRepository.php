<?php

namespace App\Settings\Infrastructure\Persistence;

use App\Settings\Domain\Entities\StorageUnitType as StorageUnitTypeEntity;
use App\Settings\Domain\Repositories\StorageUnitTypeRepositoryInterface;
use App\Settings\Domain\ValueObjects\StorageUnitTypeCode;
use App\Settings\Infrastructure\Models\StorageUnitType as StorageUnitTypeEloquent;

class EloquentStorageUnitTypeRepository implements StorageUnitTypeRepositoryInterface
{

    private function mapToDomainEntity(StorageUnitTypeEloquent $eloquentModel): StorageUnitTypeEntity
    {
        // Create a StorageUnitType domain entity
        $storageUnitType = new StorageUnitTypeEntity(
            $eloquentModel->name,
            new StorageUnitTypeCode($eloquentModel->code),
            $eloquentModel->length,
            $eloquentModel->width,
            $eloquentModel->height,
            $eloquentModel->image,
            $eloquentModel->id
        );

        return $storageUnitType;
    }

    private function mapToEloquentModel(StorageUnitTypeEntity $entity): array
    {
        return [
            'id' => $entity->getId(),
            'name' => $entity->getName(),
            'code' => $entity->getCode()->getCode(),
            'length' => $entity->getLength(),
            'width' => $entity->getWidth(),
            'height' => $entity->getHeight(),
            'image' => $entity->getImage(),
        ];
    }

    /**
     * Create a new storage unit type
     *
     * @param StorageUnitTypeEntity $entity The storage unit type domain entity to create
     * @return StorageUnitTypeEntity The created storage unit type domain entity
     */
    public function create($entity): StorageUnitTypeEntity
    {
        $data = $this->mapToEloquentModel($entity);
        $eloquentModel = StorageUnitTypeEloquent::create($data);

        return $this->mapToDomainEntity($eloquentModel);
    }

    /**
     * Update a storage unit type
     *
     * @param int|string $id The ID of the storage unit type to update
     * @param array $attributes The attributes to update
     * @return StorageUnitTypeEntity|null The updated storage unit type domain entity or null if not found
     */
    public function update($id, $attributes): ?StorageUnitTypeEntity
    {
        $eloquentModel = StorageUnitTypeEloquent::find($id);

        if (!$eloquentModel) {
            return null;
        }
        // Unset the 'code' attribute to enforce its immutability after creation
        unset($attributes['code']);

        $eloquentModel->update($attributes);
        $eloquentModel->refresh();

        return $this->mapToDomainEntity($eloquentModel);
    }

    /**
     * Check if a storage unit type with the given ID exists
     *
     * @param string $id The ID to check
     * @return bool True if the storage unit type exists, false otherwise
     */
    public function exists($id): bool
    {
        return StorageUnitTypeEloquent::whereId($id)->exists();
    }

    /**
     * Delete a storage unit type
     *
     * @param int|string $id The ID of the storage unit type to delete
     * @return bool True if the storage unit type was deleted, false otherwise
     */
    public function delete($id): bool
    {
        $eloquentModel = StorageUnitTypeEloquent::find($id);

        if (!$eloquentModel) {
            return false;
        }

        return $eloquentModel->delete();
    }

    /**
     * Find a storage unit type by its ID
     *
     * @param string $id The ID of the storage unit type to find
     * @return StorageUnitTypeEntity|null The storage unit type domain entity if found, null otherwise
     */
    public function findById($id): ?StorageUnitTypeEntity
    {
        $storageUnitType = StorageUnitTypeEloquent::find($id);

        if (!$storageUnitType) {
            return null;
        }

        $storageUnitTypeEntity = $this->mapToDomainEntity($storageUnitType);
        return $storageUnitTypeEntity;
    }

    /**
     * Retrieve all storage unit types
     *
     * @return array An array of storage unit type domain entities
     */
    public function all(): array
    {
        $storageUnitTypeEloquents = StorageUnitTypeEloquent::all();

        $storageUnitTypes = [];
        foreach ($storageUnitTypeEloquents as $eloquentModel) {
            $storageUnitTypes[] = $this->mapToDomainEntity($eloquentModel);
        }

        return $storageUnitTypes;
    }
}
