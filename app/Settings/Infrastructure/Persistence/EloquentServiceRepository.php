<?php

namespace App\Settings\Infrastructure\Persistence;

use App\Settings\Domain\Entities\Service as ServiceEntity;
use App\Settings\Domain\Repositories\ServiceRepositoryInterface;
use App\Settings\Infrastructure\Models\Service as ServiceEloquent;

class EloquentServiceRepository implements ServiceRepositoryInterface {

    private function mapToDomainEntity(ServiceEloquent $eloquentModel): ServiceEntity {
        $service = new ServiceEntity(
            $eloquentModel->name,
            $eloquentModel->description,
            $eloquentModel->id
        );

        return $service;
    }

    private function mapToEloquentModel(ServiceEntity $entity): array {
        return [
            'id' => $entity->getId(),
            'name' => $entity->getName(),
            'description' => $entity->getDescription()
        ];
    }

    /**
     * Create a new service
     *
     * @param ServiceEntity $entity The service domain entity to create
     * @return ServiceEntity The created service domain entity
     */
    public function create($entity): ServiceEntity {
        $data = $this->mapToEloquentModel($entity);
        $eloquentModel = ServiceEloquent::create($data);
        return $this->mapToDomainEntity($eloquentModel);
    }

    /**
     * Update a service
     *
     * @param int|string $id The ID of the service to update
     * @param array $attributes The attributes to update
     * @return ServiceEntity|null The updated service domain entity or null if not found
     */
    public function update($id, $attributes): ?ServiceEntity {
        $eloquentModel = ServiceEloquent::find($id);

        if (!$eloquentModel) {
            return null;
        }

        $eloquentModel->update($attributes);
        $eloquentModel->refresh();

        return $this->mapToDomainEntity($eloquentModel);
    }

    /**
     * Check if a service with the given ID exists
     *
     * @param string $id The ID to check
     * @return bool True if the service exists, false otherwise
     */
    public function exists($id): bool {
        return ServiceEloquent::whereId($id)->exists();
    }

    /**
     * Delete a service
     *
     * @param string $id The ID of the service to delete
     * @return ServiceEntity|null The deleted service domain entity or null if not found
     */
    public function delete($id): ?ServiceEntity  {
        $eloquentModel = ServiceEloquent::find($id);

        if (!$eloquentModel) {
            return null;
        }
        $serviceEntity = $this->mapToDomainEntity($eloquentModel);
        $eloquentModel->delete();
        return $serviceEntity;
    }

    /**
     * Find a service by its ID
     *
     * @param string $id The ID of the service to find
     * @return ServiceEntity|null The service domain entity if found, null otherwise
     */
    public function findById($id): ?ServiceEntity {
        $service = ServiceEloquent::find($id);

        if (!$service) {
            return null;
        }

        $serviceEntity = $this->mapToDomainEntity($service);
        return $serviceEntity;
    }

    /**
     * Retrieve all services
     *
     * @return array An array of service domain entities
     */
    public function all(): array {
        $serviceEloquents = ServiceEloquent::all();

        $services = [];
        foreach ($serviceEloquents as $eloquentModel) {
            $services[] = $this->mapToDomainEntity($eloquentModel);
        }

        return $services;
    }
}
