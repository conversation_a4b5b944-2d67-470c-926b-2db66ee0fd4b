<?php

namespace App\Settings\Infrastructure\Persistence;

use App\Settings\Domain\Entities\Material as MaterialEntity;
use App\Settings\Domain\Repositories\MaterialRepositoryInterface;
use App\Settings\Infrastructure\Models\Material as MaterialEloquent;

class EloquentMaterialRepository implements MaterialRepositoryInterface {

    private function mapToDomainEntity(MaterialEloquent $eloquentModel): MaterialEntity {
        // Create a Material domain entity
        $material = new MaterialEntity(
            $eloquentModel->name,
            $eloquentModel->threshold,
            $eloquentModel->stock_quantity,
            $eloquentModel->id
        );

        return $material;
    }

    private function mapToEloquentModel(MaterialEntity $entity): array {
        return [
            'id' => $entity->getId(),
            'name' => $entity->getName(),
            'threshold' => $entity->getThreshold(),
            'stock_quantity' => $entity->getStockQuantity(),
        ];
    }

    /**
     * Create a new material
     *
     * @param MaterialEntity $entity The material domain entity to create
     * @return MaterialEntity The created material domain entity
     */
    public function create($entity): MaterialEntity {
        $data = $this->mapToEloquentModel($entity);
        $eloquentModel = MaterialEloquent::create($data);
        return $this->mapToDomainEntity($eloquentModel);
    }

    /**
     * Update a material
     *
     * @param int|string $id The ID of the material to update
     * @param array $attributes The attributes to update
     * @return MaterialEntity|null The updated material domain entity or null if not found
     */
    public function update($id, $attributes): ?MaterialEntity {
        $eloquentModel = MaterialEloquent::find($id);

        if (!$eloquentModel) {
            return null;
        }

        $eloquentModel->update($attributes);
        $eloquentModel->refresh();

        return $this->mapToDomainEntity($eloquentModel);
    }

    /**
     * Check if a material with the given ID exists
     *
     * @param string $id The ID to check
     * @return bool True if the material exists, false otherwise
     */
    public function exists($id): bool {
        return MaterialEloquent::whereId($id)->exists();
    }

    /**
     * Delete a material
     *
     * @param int|string $id The ID of the material to delete
     * @return MaterialEntity|null The deleted material domain entity or null if not found
     */
    public function delete($id): ?MaterialEntity  {
        $eloquentModel = MaterialEloquent::find($id);

        if (!$eloquentModel) {
            return null;
        }
        $materialEntity = $this->mapToDomainEntity($eloquentModel);
        $eloquentModel->delete();
        return $materialEntity;
    }

    /**
     * Find a material by its ID
     *
     * @param int $id The ID of the material to find
     * @return MaterialEntity|null The material domain entity if found, null otherwise
     */
    public function findById($id): ?MaterialEntity {
        $material = MaterialEloquent::find($id);

        if (!$material) {
            return null;
        }

        $materialEntity = $this->mapToDomainEntity($material);
        return $materialEntity;
    }

    /**
     * Retrieve all materials
     *
     * @return array An array of material domain entities
     */
    public function all(): array {
        $materialEloquents = MaterialEloquent::all();

        $materials = [];
        foreach ($materialEloquents as $eloquentModel) {
            $materials[] = $this->mapToDomainEntity($eloquentModel);
        }

        return $materials;
    }
}
