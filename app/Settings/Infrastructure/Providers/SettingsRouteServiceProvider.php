<?php

namespace App\Settings\Infrastructure\Providers;

use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\ServiceProvider;

class SettingsRouteServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        $this->mapApiRoutes();
    }

    /**
     * Map the API routes for this bounded context.
     */
    protected function mapApiRoutes(): void
    {
        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });
        Route::middleware('api')
            ->prefix('api/settings')
            ->name('settings.')
            ->group(base_path('app/Settings/Interfaces/Http/routes.php'));
    }
}
