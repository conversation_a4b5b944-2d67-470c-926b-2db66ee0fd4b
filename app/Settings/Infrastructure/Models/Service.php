<?php

namespace App\Settings\Infrastructure\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;


/**
 * @OA\Schema(
 *     schema="Service",
 *     @OA\Property(property="id", type="string", example="abc12345"),
 *     @OA\Property(property="name", type="string", example="Small Box"),
 *     @OA\Property(property="description", type="string", example="Small box for storing small items"),
 *     @OA\Property(property="created_at", type="string", format="date-time"),
 *     @OA\Property(property="updated_at", type="string", format="date-time"),
 *     @OA\Property(property="deleted_at", type="string", format="date-time")
 * )
 */
class Service extends Model
{
    use HasFactory;
    use SoftDeletes;
    public $incrementing = false;
    protected $keyType = 'string';
    protected $table = 'services';
    protected $fillable = [
        'id',
        'name',
        'description',
    ];
}
