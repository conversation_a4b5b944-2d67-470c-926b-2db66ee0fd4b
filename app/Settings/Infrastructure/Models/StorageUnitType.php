<?php

namespace App\Settings\Infrastructure\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\WarehouseManagement\Infrastructure\Models\StorageUnit;

/**
 * @OA\Schema(
 *     schema="StorageUnitType",
 *     @OA\Property(property="id", type="string", example="abc12345"),
 *     @OA\Property(property="name", type="string", example="Small Box"),
 *     @OA\Property(property="code", type="string", example="SLF", description="Storage unit type code (SLF, VOL, FLR, SPL)"),
 *     @OA\Property(property="length", type="number", format="float", example=10.5),
 *     @OA\Property(property="width", type="number", format="float", example=8.0),
 *     @OA\Property(property="height", type="number", format="float", example=6.5),
 *     @OA\Property(property="image", type="string", nullable=true, example="small_box.jpg"),
 *     @OA\Property(property="created_at", type="string", format="date-time"),
 *     @OA\Property(property="updated_at", type="string", format="date-time")
 * )
 */
class StorageUnitType extends Model
{
    use HasFactory;
    use SoftDeletes;
    public $incrementing = false;
    protected $keyType = 'string';
    protected $table = 'storage_unit_types';
    protected $fillable = [
        'id',
        'name',
        'code',
        'length',
        'width',
        'height',
        'image'
    ];

    protected $casts = [
        'length' => 'float',
        'width' => 'float',
        'height' => 'float',
    ];
    
    /**
     * Get the storage units that use this type.
     */
    public function storageUnits()
    {
        return $this->hasMany(StorageUnit::class, 'type_id');
    }
}
