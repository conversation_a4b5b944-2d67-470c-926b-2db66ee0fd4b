<?php

namespace App\Settings\Interfaces\Http\Requests;

use App\Http\Requests\APIBaseRequest;
use App\Settings\Domain\Policies\ServicePolicy;
use Illuminate\Validation\Rule;

class CreateServiceRequest extends APIBaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $policy = app(ServicePolicy::class);
        $user = $this->user();

        return $policy->canCreate($user);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('services', 'name')
                    ->whereNull('deleted_at'),
            ],
            'description' => 'sometimes|nullable|string',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'The service name is required.',
            'name.unique' => 'A service with this name already exists.',
            'name.string' => 'The name must be a string.',
            'name.max' => 'The name must not exceed 255 characters.',
            'description.string' => 'The description must be a string.'
        ];
    }
}
