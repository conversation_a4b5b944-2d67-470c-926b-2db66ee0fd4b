<?php

namespace App\Settings\Interfaces\Http\Requests;

use App\Http\Requests\APIBaseRequest;
use App\Settings\Domain\Policies\StorageUnitTypePolicy;
use Illuminate\Validation\Rule;

class UpdateStorageUnitTypeRequest extends APIBaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $policy = app(StorageUnitTypePolicy::class);
        $user = $this->user();

        return $policy->canUpdate($user, $this->route('id'));
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => [
                'sometimes',
                'string',
                'max:255',
                Rule::unique('storage_unit_types', 'name')
                    ->whereNull('deleted_at')
                    ->ignore($this->route('id')),
            ],
            'length' => 'sometimes|numeric|min:0.01',
            'width' => 'sometimes|numeric|min:0.01',
            'height' => 'sometimes|numeric|min:0.01',
            'image' => 'nullable|string|max:500', // S3 object key
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.unique' => 'A storage unit type with this name already exists.',
            'length.min' => 'The length must be greater than 0.',
            'width.min' => 'The width must be greater than 0.',
            'height.min' => 'The height must be greater than 0.',
            'image.string' => 'The image must be a valid string (future S3 object key).',
            'image.max' => 'The image field must not exceed 500 characters.',
        ];
    }
}
