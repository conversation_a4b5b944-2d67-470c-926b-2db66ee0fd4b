<?php

namespace App\Settings\Interfaces\Http\Requests;

use App\Http\Requests\APIBaseRequest;
use App\Settings\Domain\Policies\ServicePolicy;

class ListServicesRequest extends APIBaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $policy = app(ServicePolicy::class);
        $user = $this->user();

        return $policy->canView($user, null);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // No pagination parameters needed as per requirements
        ];
    }
}
