<?php

namespace App\Settings\Interfaces\Http\Requests;

use App\Http\Requests\APIBaseRequest;
use App\Settings\Domain\Policies\MaterialPolicy;
use Illuminate\Validation\Rule;

class CreateMaterialRequest extends APIBaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $policy = app(MaterialPolicy::class);
        $user = $this->user();

        return $policy->canCreate($user);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('materials', 'name')
                    ->whereNull('deleted_at'),
            ],
            'threshold' => 'required|integer|min:1',
            'stock_quantity' => 'required|integer|min:0',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'The material name is required.',
            'name.unique' => 'A material with this name already exists.',
            'name.string' => 'The name must be a string.',
            'name.max' => 'The name must not exceed 255 characters.',
            'threshold.required' => 'The threshold is required.',
            'threshold.integer' => 'The threshold must be an integer.',
            'threshold.min' => 'The threshold must be greater than 0.',
            'stock_quantity.required' => 'The stock quantity is required.',
            'stock_quantity.integer' => 'The stock quantity must be an integer.',
            'stock_quantity.min' => 'The stock quantity must be greater than or equal to 0.',
        ];
    }
}
