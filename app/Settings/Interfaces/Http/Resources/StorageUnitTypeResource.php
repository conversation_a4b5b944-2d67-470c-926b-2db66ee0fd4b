<?php

namespace App\Settings\Interfaces\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @OA\Schema(
 *     schema="StorageUnitTypeResource",
 *     @OA\Property(property="id", type="string", example="abc12345"),
 *     @OA\Property(property="name", type="string", example="Small Box"),
 *     @OA\Property(property="code", type="string", example="SLF", description="Storage unit type code (SLF, VOL, FLR, SPL)"),
 *     @OA\Property(property="length", type="number", format="float", example=10.5),
 *     @OA\Property(property="width", type="number", format="float", example=8.0),
 *     @OA\Property(property="height", type="number", format="float", example=6.5),
 *     @OA\Property(property="image", type="string", nullable=true, example=null, description="Image identifier (reserved for future S3 implementation)")
 * )
 */
class StorageUnitTypeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'code' => $this->getCode()->getCode(),
            'length' => $this->getLength(),
            'width' => $this->getWidth(),
            'height' => $this->getHeight(),
            'image' => $this->getImage(),
        ];
    }
}
