<?php

namespace App\Settings\Interfaces\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @OA\Schema(
 *     schema="ServiceResource",
 *     @OA\Property(property="id", type="string", example="service-123"),
 *     @OA\Property(property="name", type="string", example="Test Service"),
 *     @OA\Property(property="description", type="string", example="This is a test service")
 * )
 */
class ServiceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'description' => $this->getDescription(),
        ];
    }
}
