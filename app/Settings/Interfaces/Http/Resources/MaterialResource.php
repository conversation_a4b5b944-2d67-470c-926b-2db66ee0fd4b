<?php

namespace App\Settings\Interfaces\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @OA\Schema(
 *     schema="MaterialResource",
 *     @OA\Property(property="id", type="string", example="material-123"),
 *     @OA\Property(property="name", type="string", example="Small Box"),
 *     @OA\Property(property="threshold", type="integer", example=100),
 *     @OA\Property(property="stock_quantity", type="integer", example=100)
 * )
 */
class MaterialResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'threshold' => $this->getThreshold(),
            'stock_quantity' => $this->getStockQuantity(),
        ];
    }
}
