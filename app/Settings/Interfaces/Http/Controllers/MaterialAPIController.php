<?php

namespace App\Settings\Interfaces\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use App\Settings\Interfaces\Http\Resources\MaterialResource;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use App\Settings\Application\Services\MaterialService;
use App\Settings\Domain\Entities\Material;
use App\Settings\Interfaces\Http\Requests\ListMaterialsRequest;
use App\Settings\Interfaces\Http\Resources\MaterialCollection;
use App\Settings\Interfaces\Http\Requests\CreateMaterialRequest;
use App\Settings\Interfaces\Http\Requests\UpdateMaterialRequest;

class MaterialAPIController extends Controller {
    use AuthorizesRequests;

    private MaterialService $materialService;

    public function __construct(MaterialService $materialService) {
        $this->materialService = $materialService;
    }

    /**
     * @OA\Get(
     *     path="/api/settings/materials",
     *     summary="Get all materials",
     *     description="Retrieve a list of all materials",
     *     operationId="getMaterials",
     *     tags={"Materials"},
     *     @OA\Response(
     *         response=200,
     *         description="Materials retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="data", type="array", @OA\Items(ref="#/components/schemas/MaterialResource")),
     *             @OA\Property(property="message", type="string", example="Materials retrieved successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - Insufficient permissions",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="You do not have permission to perform this action."),
     *             @OA\Property(property="code", type="string", example="INSUFFICIENT_PERMISSIONS"),
     *             @OA\Property(property="errors", type="array", @OA\Items(type="string"), example={})
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Internal server error",
     *         @OA\JsonContent(ref="#/components/schemas/InternalServerErrorResponse")
     *     ),
     *     security={{"jwt_auth":{}}}
     * )
     */
    public function index(ListMaterialsRequest $request): JsonResponse {
        $materials = $this->materialService->getAllMaterials();

        return (new MaterialCollection($materials))
            ->additional([
                'message' => 'Materials retrieved successfully'
            ])
            ->response();
    }

    /**
     * @OA\Post(
     *     path="/api/settings/materials",
     *     summary="Create a new material",
     *     description="Create a new material with the provided details",
     *     operationId="createMaterial",
     *     tags={"Materials"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"name", "threshold", "stock_quantity"},
     *             @OA\Property(property="name", type="string", example="Small Box"),
     *             @OA\Property(property="threshold", type="integer", example=100),
     *             @OA\Property(property="stock_quantity", type="integer", example=200)
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Material created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="data", ref="#/components/schemas/MaterialResource"),
     *             @OA\Property(property="message", type="string", example="Material created successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="code", type="string", example="VALIDATION_ERROR"),
     *             @OA\Property(property="message", type="string", example="The given data was invalid."),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 example={"name": {"The name field is required."}}
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - Insufficient permissions",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="You do not have permission to perform this action."),
     *             @OA\Property(property="code", type="string", example="INSUFFICIENT_PERMISSIONS"),
     *             @OA\Property(property="errors", type="array", @OA\Items(type="string"), example={})
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Internal server error",
     *         @OA\JsonContent(ref="#/components/schemas/InternalServerErrorResponse")
     *     ),
     *     security={{"jwt_auth":{}}}
     * )
     */
    public function store(CreateMaterialRequest $request): JsonResponse {
        $validated = $request->validated();
        $material = $this->materialService->createMaterial($validated);

        return (new MaterialResource($material))
            ->additional([
                'message' => 'Material created successfully'
            ])
            ->response()
            ->setStatusCode(201);
    }

    /**
     * @OA\Get(
     *     path="/api/settings/materials/{id}",
     *     summary="Get a specific material",
     *     description="Retrieve a specific material by its ID",
     *     operationId="getMaterial",
     *     tags={"Materials"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="Material ID",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Material retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="data", ref="#/components/schemas/MaterialResource"),
     *             @OA\Property(property="message", type="string", example="Material retrieved successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Material not found",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Material not found."),
     *             @OA\Property(property="code", type="string", example="NOT_FOUND"),
     *             @OA\Property(property="errors", type="array", @OA\Items(type="string"), example={})
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - Insufficient permissions",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="You do not have permission to perform this action."),
     *             @OA\Property(property="code", type="string", example="INSUFFICIENT_PERMISSIONS"),
     *             @OA\Property(property="errors", type="array", @OA\Items(type="string"), example={})
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Internal server error",
     *         @OA\JsonContent(ref="#/components/schemas/InternalServerErrorResponse")
     *     ),
     *     security={{"jwt_auth":{}}}
     * )
     */
    public function show(string $id): JsonResponse {
        $this->authorize('view', Material::class);

        $material = $this->materialService->getMaterial($id);

        if (!$material) {
            throw new NotFoundHttpException('Material not found');
        }

        return (new MaterialResource($material))
            ->additional([
                'message' => 'Material retrieved successfully'
            ])
            ->response();
    }

    /**
     * @OA\Put(
     *     path="/api/settings/materials/{id}",
     *     summary="Update a material",
     *     description="Update an existing material with the provided details",
     *     operationId="updateMaterial",
     *     tags={"Materials"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="Material ID",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="name", type="string", example="Small Box"),
     *             @OA\Property(property="threshold", type="integer", example=100),
     *             @OA\Property(property="stock_quantity", type="integer", example=200)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Material updated successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="data", ref="#/components/schemas/MaterialResource"),
     *             @OA\Property(property="message", type="string", example="Material updated successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Material not found",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Material not found."),
     *             @OA\Property(property="code", type="string", example="NOT_FOUND"),
     *             @OA\Property(property="errors", type="array", @OA\Items(type="string"), example={})
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="code", type="string", example="VALIDATION_ERROR"),
     *             @OA\Property(property="message", type="string", example="The given data was invalid."),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 example={"name": {"A material with this name already exists."}}
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - Insufficient permissions",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="You do not have permission to perform this action."),
     *             @OA\Property(property="code", type="string", example="INSUFFICIENT_PERMISSIONS"),
     *             @OA\Property(property="errors", type="array", @OA\Items(type="string"), example={})
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Internal server error",
     *         @OA\JsonContent(ref="#/components/schemas/InternalServerErrorResponse")
     *     ),
     *     security={{"jwt_auth":{}}}
     * )
     */
    public function update(UpdateMaterialRequest $request, string $id): JsonResponse {
        $validated = $request->validated();
        $material = $this->materialService->updateMaterial($id, $validated);

        if (!$material) {
            throw new NotFoundHttpException('Material not found');
        }

        return (new MaterialResource($material))
            ->additional([
                'message' => 'Material updated successfully'
            ])
            ->response();
    }

    /**
     * @OA\Delete(
     *     path="/api/settings/materials/{id}",
     *     summary="Delete a material",
     *     description="Delete an existing material",
     *     operationId="deleteMaterial",
     *     tags={"Materials"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="Material ID",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Material deleted successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Material deleted successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Material not found",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Material not found."),
     *             @OA\Property(property="code", type="string", example="NOT_FOUND"),
     *             @OA\Property(property="errors", type="array", @OA\Items(type="string"), example={})
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - Insufficient permissions",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="You do not have permission to perform this action."),
     *             @OA\Property(property="code", type="string", example="INSUFFICIENT_PERMISSIONS"),
     *             @OA\Property(property="errors", type="array", @OA\Items(type="string"), example={})
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Internal server error",
     *         @OA\JsonContent(ref="#/components/schemas/InternalServerErrorResponse")
     *     ),
     *     security={{"jwt_auth":{}}}
     * )
     */
    public function destroy(string $id): JsonResponse {
        $this->authorize('delete', Material::class);

        $deleted = $this->materialService->deleteMaterial($id);

        if (!$deleted) {
            throw new NotFoundHttpException('Material not found');
        }

        return (new MaterialResource($deleted))
            ->additional([
                'message' => 'Material deleted successfully'
            ])
            ->response();
    }
}
