<?php

namespace App\Settings\Interfaces\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use App\Settings\Application\Services\ServiceService;
use App\Settings\Domain\Entities\Service;
use App\Settings\Interfaces\Http\Requests\ListServicesRequest;
use App\Settings\Interfaces\Http\Resources\ServiceCollection;
use App\Settings\Interfaces\Http\Requests\CreateServiceRequest;
use App\Settings\Interfaces\Http\Requests\UpdateServiceRequest;
use App\Settings\Interfaces\Http\Resources\ServiceResource;

class ServiceAPIController extends Controller {
    use AuthorizesRequests;

    private ServiceService $serviceService;

    public function __construct(ServiceService $serviceService) {
        $this->serviceService = $serviceService;
    }

    /**
     * @OA\Get(
     *     path="/api/settings/services",
     *     summary="Get all services",
     *     description="Retrieve a list of all services",
     *     operationId="getServices",
     *     tags={"Services"},
     *     @OA\Response(
     *         response=200,
     *         description="Services retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="data", type="array", @OA\Items(ref="#/components/schemas/ServiceResource")),
     *             @OA\Property(property="message", type="string", example="Services retrieved successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - Insufficient permissions",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="You do not have permission to perform this action."),
     *             @OA\Property(property="code", type="string", example="INSUFFICIENT_PERMISSIONS"),
     *             @OA\Property(property="errors", type="array", @OA\Items(type="string"), example={})
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Internal server error",
     *         @OA\JsonContent(ref="#/components/schemas/InternalServerErrorResponse")
     *     ),
     *     security={{"jwt_auth":{}}}
     * )
     */
    public function index(ListServicesRequest $request): JsonResponse {
        $services = $this->serviceService->getAllServices();

        return (new ServiceCollection($services))
            ->additional([
                'message' => 'Services retrieved successfully'
            ])
            ->response();
    }

    /**
     * @OA\Post(
     *     path="/api/settings/services",
     *     summary="Create a new service",
     *     description="Create a new service with the provided details",
     *     operationId="createService",
     *     tags={"Services"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"name", "description"},
     *             @OA\Property(property="name", type="string", example="Small Box"),
     *             @OA\Property(property="description", type="string", example="Small box for storing small items")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Service created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="data", ref="#/components/schemas/ServiceResource"),
     *             @OA\Property(property="message", type="string", example="Service created successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="code", type="string", example="VALIDATION_ERROR"),
     *             @OA\Property(property="message", type="string", example="The given data was invalid."),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 example={"name": {"The name field is required."}}
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - Insufficient permissions",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="You do not have permission to perform this action."),
     *             @OA\Property(property="code", type="string", example="INSUFFICIENT_PERMISSIONS"),
     *             @OA\Property(property="errors", type="array", @OA\Items(type="string"), example={})
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Internal server error",
     *         @OA\JsonContent(ref="#/components/schemas/InternalServerErrorResponse")
     *     ),
     *     security={{"jwt_auth":{}}}
     * )
     */
    public function store(CreateServiceRequest $request): JsonResponse {
        $validated = $request->validated();
        $service = $this->serviceService->createService($validated);

        return (new ServiceResource($service))
            ->additional([
                'message' => 'Service created successfully'
            ])
            ->response()
            ->setStatusCode(201);
    }

    /**
     * @OA\Get(
     *     path="/api/settings/services/{id}",
     *     summary="Get a specific service",
     *     description="Retrieve a specific service by its ID",
     *     operationId="getService",
     *     tags={"Services"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="Service ID",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Service retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="data", ref="#/components/schemas/ServiceResource"),
     *             @OA\Property(property="message", type="string", example="Service retrieved successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Service not found",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Service not found."),
     *             @OA\Property(property="code", type="string", example="NOT_FOUND"),
     *             @OA\Property(property="errors", type="array", @OA\Items(type="string"), example={})
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - Insufficient permissions",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="You do not have permission to perform this action."),
     *             @OA\Property(property="code", type="string", example="INSUFFICIENT_PERMISSIONS"),
     *             @OA\Property(property="errors", type="array", @OA\Items(type="string"), example={})
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Internal server error",
     *         @OA\JsonContent(ref="#/components/schemas/InternalServerErrorResponse")
     *     ),
     *     security={{"jwt_auth":{}}}
     * )
     */
    public function show(string $id): JsonResponse {
        $this->authorize('view', Service::class);

        $service = $this->serviceService->getService($id);

        if (!$service) {
            throw new NotFoundHttpException('Service not found');
        }

        return (new ServiceResource($service))
            ->additional([
                'message' => 'Service retrieved successfully'
            ])
            ->response();
    }

    /**
     * @OA\Put(
     *     path="/api/settings/services/{id}",
     *     summary="Update a service",
     *     description="Update an existing service with the provided details",
     *     operationId="updateService",
     *     tags={"Services"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="Service ID",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="name", type="string", example="Small Box"),
     *             @OA\Property(property="description", type="string", example="Small box for storing small items")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Service updated successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="data", ref="#/components/schemas/ServiceResource"),
     *             @OA\Property(property="message", type="string", example="Service updated successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Service not found",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Service not found."),
     *             @OA\Property(property="code", type="string", example="NOT_FOUND"),
     *             @OA\Property(property="errors", type="array", @OA\Items(type="string"), example={})
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="code", type="string", example="VALIDATION_ERROR"),
     *             @OA\Property(property="message", type="string", example="The given data was invalid."),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 example={"name": {"A service with this name already exists."}}
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - Insufficient permissions",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="You do not have permission to perform this action."),
     *             @OA\Property(property="code", type="string", example="INSUFFICIENT_PERMISSIONS"),
     *             @OA\Property(property="errors", type="array", @OA\Items(type="string"), example={})
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Internal server error",
     *         @OA\JsonContent(ref="#/components/schemas/InternalServerErrorResponse")
     *     ),
     *     security={{"jwt_auth":{}}}
     * )
     */
    public function update(UpdateServiceRequest $request, string $id): JsonResponse {
        $validated = $request->validated();
        $service = $this->serviceService->updateService($id, $validated);

        if (!$service) {
            throw new NotFoundHttpException('Service not found');
        }

        return (new ServiceResource($service))
            ->additional([
                'message' => 'Service updated successfully'
            ])
            ->response();
    }

    /**
     * @OA\Delete(
     *     path="/api/settings/services/{id}",
     *     summary="Delete a service",
     *     description="Delete an existing service",
     *     operationId="deleteService",
     *     tags={"Services"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="Service ID",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Service deleted successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Service deleted successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Service not found",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Service not found."),
     *             @OA\Property(property="code", type="string", example="NOT_FOUND"),
     *             @OA\Property(property="errors", type="array", @OA\Items(type="string"), example={})
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - Insufficient permissions",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="You do not have permission to perform this action."),
     *             @OA\Property(property="code", type="string", example="INSUFFICIENT_PERMISSIONS"),
     *             @OA\Property(property="errors", type="array", @OA\Items(type="string"), example={})
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Internal server error",
     *         @OA\JsonContent(ref="#/components/schemas/InternalServerErrorResponse")
     *     ),
     *     security={{"jwt_auth":{}}}
     * )
     */
    public function destroy(string $id): JsonResponse {
        $this->authorize('delete', Service::class);

        $deleted = $this->serviceService->deleteService($id);

        if (!$deleted) {
            throw new NotFoundHttpException('Service not found');
        }

        return (new ServiceResource($deleted))
            ->additional([
                'message' => 'Service deleted successfully'
            ])
            ->response();
    }
}
