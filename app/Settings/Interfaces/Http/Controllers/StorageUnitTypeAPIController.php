<?php

namespace App\Settings\Interfaces\Http\Controllers;

use App\Settings\Application\Services\StorageUnitTypeService;
use App\Settings\Interfaces\Http\Requests\CreateStorageUnitTypeRequest;
use App\Settings\Interfaces\Http\Requests\UpdateStorageUnitTypeRequest;
use App\Settings\Interfaces\Http\Requests\ListStorageUnitTypesRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use App\Settings\Interfaces\Http\Resources\StorageUnitTypeResource;
use App\Settings\Interfaces\Http\Resources\StorageUnitTypeCollection;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use App\Settings\Domain\Entities\StorageUnitType;

class StorageUnitTypeAPIController extends Controller
{
    use AuthorizesRequests;

    private StorageUnitTypeService $storageUnitTypeService;

    public function __construct(StorageUnitTypeService $storageUnitTypeService)
    {
        $this->storageUnitTypeService = $storageUnitTypeService;
    }

    /**
     * @OA\Get(
     *     path="/api/settings/storage-unit-types",
     *     summary="Get all storage unit types",
     *     description="Retrieve a list of all storage unit types",
     *     operationId="getStorageUnitTypes",
     *     tags={"Storage Unit Types"},
     *     @OA\Response(
     *         response=200,
     *         description="Storage unit types retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="data", type="array", @OA\Items(ref="#/components/schemas/StorageUnitTypeResource")),
     *             @OA\Property(property="message", type="string", example="Storage unit types retrieved successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - Insufficient permissions",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="You do not have permission to perform this action."),
     *             @OA\Property(property="code", type="string", example="INSUFFICIENT_PERMISSIONS"),
     *             @OA\Property(property="errors", type="array", @OA\Items(type="string"), example={})
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Internal server error",
     *         @OA\JsonContent(ref="#/components/schemas/InternalServerErrorResponse")
     *     ),
     *     security={{"jwt_auth":{}}}
     * )
     */
    public function index(ListStorageUnitTypesRequest $request): JsonResponse
    {
        $storageUnitTypes = $this->storageUnitTypeService->getAllStorageUnitTypes();

        return (new StorageUnitTypeCollection($storageUnitTypes))
            ->additional([
                'message' => 'Storage unit types retrieved successfully'
            ])
            ->response();
    }

    /**
     * @OA\Post(
     *     path="/api/settings/storage-unit-types",
     *     summary="Create a new storage unit type",
     *     description="Create a new storage unit type with the provided details",
     *     operationId="createStorageUnitType",
     *     tags={"Storage Unit Types"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"name", "code", "length", "width", "height"},
     *             @OA\Property(property="name", type="string", example="Small Box"),
     *             @OA\Property(property="code", type="string", example="SLF", description="Storage unit type code (SLF, VOL, FLR, SPL)"),
     *             @OA\Property(property="length", type="number", format="float", example=10.5),
     *             @OA\Property(property="width", type="number", format="float", example=8.0),
     *             @OA\Property(property="height", type="number", format="float", example=6.5),
     *             @OA\Property(property="image", type="string", nullable=true, example=null, description="Image identifier (reserved for future S3 implementation)")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Storage unit type created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="data", ref="#/components/schemas/StorageUnitTypeResource"),
     *             @OA\Property(property="message", type="string", example="Storage unit type created successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="code", type="string", example="VALIDATION_ERROR"),
     *             @OA\Property(property="message", type="string", example="The given data was invalid."),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 example={"name": {"The name field is required."}}
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - Insufficient permissions",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="You do not have permission to perform this action."),
     *             @OA\Property(property="code", type="string", example="INSUFFICIENT_PERMISSIONS"),
     *             @OA\Property(property="errors", type="array", @OA\Items(type="string"), example={})
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Internal server error",
     *         @OA\JsonContent(ref="#/components/schemas/InternalServerErrorResponse")
     *     ),
     *     security={{"jwt_auth":{}}}
     * )
     */
    public function store(CreateStorageUnitTypeRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $storageUnitType = $this->storageUnitTypeService->createStorageUnitType($validated);

        return (new StorageUnitTypeResource($storageUnitType))
            ->additional([
                'message' => 'Storage unit type created successfully'
            ])
            ->response()
            ->setStatusCode(201);
    }

    /**
     * @OA\Get(
     *     path="/api/settings/storage-unit-types/{id}",
     *     summary="Get a specific storage unit type",
     *     description="Retrieve a specific storage unit type by its ID",
     *     operationId="getStorageUnitType",
     *     tags={"Storage Unit Types"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="Storage unit type ID",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Storage unit type retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="data", ref="#/components/schemas/StorageUnitTypeResource"),
     *             @OA\Property(property="message", type="string", example="Storage unit type retrieved successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Storage unit type not found",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Storage unit type not found."),
     *             @OA\Property(property="code", type="string", example="NOT_FOUND"),
     *             @OA\Property(property="errors", type="array", @OA\Items(type="string"), example={})
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - Insufficient permissions",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="You do not have permission to perform this action."),
     *             @OA\Property(property="code", type="string", example="INSUFFICIENT_PERMISSIONS"),
     *             @OA\Property(property="errors", type="array", @OA\Items(type="string"), example={})
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Internal server error",
     *         @OA\JsonContent(ref="#/components/schemas/InternalServerErrorResponse")
     *     ),
     *     security={{"jwt_auth":{}}}
     * )
     */
    public function show(string $id): JsonResponse
    {
        $this->authorize('view', StorageUnitType::class);

        $storageUnitType = $this->storageUnitTypeService->getStorageUnitType($id);

        if (!$storageUnitType) {
            throw new NotFoundHttpException('Storage unit type not found');
        }

        return (new StorageUnitTypeResource($storageUnitType))
            ->additional([
                'message' => 'Storage unit type retrieved successfully'
            ])
            ->response();
    }

    /**
     * @OA\Put(
     *     path="/api/settings/storage-unit-types/{id}",
     *     summary="Update a storage unit type",
     *     description="Update an existing storage unit type with the provided details",
     *     operationId="updateStorageUnitType",
     *     tags={"Storage Unit Types"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="Storage unit type ID",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="name", type="string", example="Medium Box"),
     *             @OA\Property(property="length", type="number", format="float", example=15.5),
     *             @OA\Property(property="width", type="number", format="float", example=12.0),
     *             @OA\Property(property="height", type="number", format="float", example=10.5),
     *             @OA\Property(property="image", type="string", nullable=true, example=null, description="Image identifier (reserved for future S3 implementation)"),
     *             description="Note: Code field is not editable after creation"
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Storage unit type updated successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="data", ref="#/components/schemas/StorageUnitTypeResource"),
     *             @OA\Property(property="message", type="string", example="Storage unit type updated successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Storage unit type not found",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Storage unit type not found."),
     *             @OA\Property(property="code", type="string", example="NOT_FOUND"),
     *             @OA\Property(property="errors", type="array", @OA\Items(type="string"), example={})
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="code", type="string", example="VALIDATION_ERROR"),
     *             @OA\Property(property="message", type="string", example="The given data was invalid."),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 example={"name": {"The name field is required."}}
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - Insufficient permissions",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="You do not have permission to perform this action."),
     *             @OA\Property(property="code", type="string", example="INSUFFICIENT_PERMISSIONS"),
     *             @OA\Property(property="errors", type="array", @OA\Items(type="string"), example={})
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Internal server error",
     *         @OA\JsonContent(ref="#/components/schemas/InternalServerErrorResponse")
     *     ),
     *     security={{"jwt_auth":{}}}
     * )
     */
    public function update(UpdateStorageUnitTypeRequest $request, string $id): JsonResponse
    {
        $validated = $request->validated();
        $storageUnitType = $this->storageUnitTypeService->updateStorageUnitType($id, $validated);

        if (!$storageUnitType) {
            throw new NotFoundHttpException('Storage unit type not found');
        }

        return (new StorageUnitTypeResource($storageUnitType))
            ->additional([
                'message' => 'Storage unit type updated successfully'
            ])
            ->response();
    }

    /**
     * @OA\Delete(
     *     path="/api/settings/storage-unit-types/{id}",
     *     summary="Delete a storage unit type",
     *     description="Delete an existing storage unit type",
     *     operationId="deleteStorageUnitType",
     *     tags={"Storage Unit Types"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="Storage unit type ID",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Storage unit type deleted successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Storage unit type deleted successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Storage unit type not found",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Storage unit type not found."),
     *             @OA\Property(property="code", type="string", example="NOT_FOUND"),
     *             @OA\Property(property="errors", type="array", @OA\Items(type="string"), example={})
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - Insufficient permissions",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="You do not have permission to perform this action."),
     *             @OA\Property(property="code", type="string", example="INSUFFICIENT_PERMISSIONS"),
     *             @OA\Property(property="errors", type="array", @OA\Items(type="string"), example={})
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity - Storage unit type is in use",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Cannot delete storage unit type because it is being used by existing storage units"),
     *             @OA\Property(property="code", type="string", example="BUSINESS_RULE_VIOLATION"),
     *             @OA\Property(property="errors", type="array", @OA\Items(type="string"), example={})
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Internal server error",
     *         @OA\JsonContent(ref="#/components/schemas/InternalServerErrorResponse")
     *     ),
     *     security={{"jwt_auth":{}}}
     * )
     */
    public function destroy(string $id): JsonResponse
    {
        $this->authorize('delete', StorageUnitType::class);

        $result = $this->storageUnitTypeService->deleteStorageUnitType($id);

        if (!$result) {
            throw new NotFoundHttpException('Storage unit type not found');
        }

        return response()->json([
            'message' => 'Storage unit type deleted successfully'
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/settings/storage-unit-types/codes",
     *     summary="Get all storage unit type codes",
     *     description="Retrieve all available storage unit type codes for frontend use",
     *     operationId="getStorageUnitTypeCodes",
     *     tags={"Storage Unit Types"},
     *     @OA\Response(
     *         response=200,
     *         description="Storage unit type codes retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="data", type="array", @OA\Items(type="string"), example={"SLF", "VOL", "FLR", "SPL"}),
     *             @OA\Property(property="message", type="string", example="Storage unit type codes retrieved successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(ref="#/components/schemas/UnauthorizedResponse")
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Insufficient permissions"),
     *             @OA\Property(property="code", type="string", example="INSUFFICIENT_PERMISSIONS"),
     *             @OA\Property(property="errors", type="array", @OA\Items(type="string"), example={})
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Internal server error",
     *         @OA\JsonContent(ref="#/components/schemas/InternalServerErrorResponse")
     *     ),
     *     security={{"jwt_auth":{}}}
     * )
     */
    public function codes(): JsonResponse
    {
        $this->authorize('view', StorageUnitType::class);

        $codes = $this->storageUnitTypeService->getStorageUnitTypeCodes();

        return response()->json([
            'data' => $codes,
            'message' => 'Storage unit type codes retrieved successfully'
        ]);
    }
}
