<?php

namespace App\Settings\Domain\ValueObjects;

use App\Core\ValueObjects\ValueObject;
use InvalidArgumentException;

/**
 * Storage Unit Type Code value object representing the type classification of storage units.
 */
class StorageUnitTypeCode extends ValueObject
{
    /**
     * Known storage unit type codes in the system.
     */
    public const SHELF = 'SLF';
    public const VOLUME = 'VOL';
    public const FLOOR = 'FLR';
    public const SPECIAL = 'SPL';

    /**
     * List of all valid storage unit type codes.
     *
     * @var array<string>
     */
    private static array $validCodes = [
        self::SHELF,
        self::VOLUME,
        self::FLOOR,
        self::SPECIAL,
    ];

    /**
     * The code value.
     *
     * @var string
     */
    private string $code;

    /**
     * Create a new StorageUnitTypeCode instance.
     *
     * @param string $code
     * @throws InvalidArgumentException
     */
    public function __construct(string $code)
    {
        $this->setCode($code);
    }

    /**
     * Get the code value.
     *
     * @return string
     */
    public function getCode(): string
    {
        return $this->code;
    }

    /**
     * Set the code value.
     *
     * @param string $code
     * @return self
     * @throws InvalidArgumentException
     */
    private function setCode(string $code): self
    {
        if (!in_array($code, self::$validCodes)) {
            throw new InvalidArgumentException("Invalid storage unit type code: {$code}");
        }

        $this->code = $code;
        return $this;
    }

    /**
     * Check if the code equals another code.
     *
     * @param ValueObject $object
     * @return bool
     */
    public function equals(ValueObject $object): bool
    {
        if (!$object instanceof StorageUnitTypeCode) {
            return false;
        }

        return $this->code === $object->getCode();
    }

    /**
     * Create a code from a string.
     *
     * @param string $code
     * @return self
     */
    public static function fromString(string $code): self
    {
        return new self($code);
    }

    /**
     * Check if a code is valid.
     *
     * @param string $code
     * @return bool
     */
    public static function isValid(string $code): bool
    {
        return in_array($code, self::$validCodes);
    }

    /**
     * Get all valid storage unit type codes.
     *
     * @return array<string>
     */
    public static function getAllCodes(): array
    {
        return self::$validCodes;
    }

    /**
     * Get the string representation of the code.
     *
     * @return string
     */
    public function __toString(): string
    {
        return $this->code;
    }
}
