<?php

namespace App\Settings\Domain\Entities;

use App\Core\Domain\Entity;
use DomainException;

class Material extends Entity {
    private string $name;
    private int $threshold;
    private int $stock_quantity;
    

    public function __construct(
        string $name,
        int $threshold,
        int $stock_quantity,
        ?string $id = null,
    ) {
        if (empty(trim($name))) {
            throw new DomainException("Material name cannot be empty");
        }
        
        if ($threshold <= 0) {
            throw new DomainException("Threshold must be greater than zero");
        }

        if ($stock_quantity < 0) {
            throw new DomainException("Stock quantity must be greater than or equal to zero");
        }

        $this->setId($id);
        $this->name = $name;
        $this->threshold = $threshold;
        $this->stock_quantity = $stock_quantity;
    }

    public function getName(): string {
        return $this->name;
    }

    public function getThreshold(): int {
        return $this->threshold;
    }

    public function getStockQuantity(): int {
        return $this->stock_quantity;
    }
}
