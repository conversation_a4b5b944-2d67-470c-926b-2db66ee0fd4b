<?php

namespace App\Settings\Domain\Entities;

use App\Core\Domain\Entity;
use DomainException;

class Service extends Entity {
    private string $name;
    private ?string $description;
    

    public function __construct(
        string $name,
        ?string $description = null,
        ?string $id = null,
    ) {
        if (empty(trim($name))) {
            throw new DomainException("Service name cannot be empty");
        }
    
        $this->setId($id);
        $this->name = $name;
        $this->description = $description;
    }

    public function getName(): string {
        return $this->name;
    }

    public function getDescription(): ?string {
        return $this->description;
    }

    public function updateName(string $name): void {
        if (empty(trim($name))) {
            throw new DomainException("Service name cannot be empty");
        }
        $this->name = $name;
    }

    public function updateDescription(?string $description): void {
        $this->description = $description;
    }
}
