<?php

namespace App\Settings\Domain\Entities;

use App\Core\Domain\Entity;
use App\Settings\Domain\ValueObjects\StorageUnitTypeCode;
use DomainException;

class StorageUnitType extends Entity {
    private string $name;
    private StorageUnitTypeCode $code; // Immutable after creation
    private float $length;
    private float $width;
    private float $height;
    private ?string $image;

    public function __construct(
        string $name,
        StorageUnitTypeCode $code,
        float $length,
        float $width,
        float $height,
        ?string $image = null,
        ?string $id = null,
    ) {
        if (empty(trim($name))) {
            throw new DomainException("Storage unit type name cannot be empty");
        }
        if ($length <= 0) {
            throw new DomainException("Length must be greater than zero");
        }
        if ($width <= 0) {
            throw new DomainException("Width must be greater than zero");
        }
        if ($height <= 0) {
            throw new DomainException("Height must be greater than zero");
        }

        if ($id !== null) {
            $this->setId($id);
        }

        $this->name = $name;
        $this->code = $code;
        $this->length = $length;
        $this->width = $width;
        $this->height = $height;
        $this->image = $image;
    }

    public function getName(): string {
        return $this->name;
    }

    public function getCode(): StorageUnitTypeCode {
        return $this->code;
    }

    public function getLength(): float {
        return $this->length;
    }

    public function getWidth(): float {
        return $this->width;
    }

    public function getHeight(): float {
        return $this->height;
    }

    public function getImage(): ?string {
        return $this->image;
    }

    public function updateName(string $name): void {
        if (empty(trim($name))) {
            throw new DomainException("Storage unit type name cannot be empty");
        }
        $this->name = $name;
    }

    public function updateDimensions(float $length, float $width, float $height): void {
        if ($length <= 0) {
            throw new DomainException("Length must be greater than zero");
        }
        if ($width <= 0) {
            throw new DomainException("Width must be greater than zero");
        }
        if ($height <= 0) {
            throw new DomainException("Height must be greater than zero");
        }

        $this->length = $length;
        $this->width = $width;
        $this->height = $height;
    }

    public function updateImage(?string $image): void {
        $this->image = $image;
    }
}
