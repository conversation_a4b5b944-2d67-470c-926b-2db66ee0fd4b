<?php

namespace App\Settings\Domain\Policies;

use App\Core\Auth\Constants\Permission;
use App\Core\Auth\Models\AuthUser;
use App\Core\Auth\Policies\BasePolicy;
use App\Core\Auth\Services\PermissionService;
use App\Core\Auth\ValueObjects\Role;
use App\Settings\Domain\Entities\Service;

class ServicePolicy extends BasePolicy
{
    private PermissionService $permissionService;

    public function __construct(PermissionService $permissionService)
    {
        $this->permissionService = $permissionService;
    }

    /**
     * Check if the user can view services.
     *
     * @param AuthUser $user
     * @param mixed $service
     * @return bool
     */
    public function canView(AuthUser $user, $service = null): bool
    {
        // Check if the user has the settings.view permission
        if (!$this->permissionService->hasPermission($user, Permission::SETTINGS_VIEW)) {
            return false;
        }

        // Admin and warehouse_manager can view settings
        return $user->hasRole(Role::ADMIN) || $user->hasRole(Role::WAREHOUSE_MANAGER);
    }

    /**
     * Check if the user can create services.
     *
     * @param AuthUser $user
     * @return bool
     */
    public function canCreate(AuthUser $user): bool
    {
        // Check if the user has the settings.create permission
        if (!$this->permissionService->hasPermission($user, Permission::SETTINGS_CREATE)) {
            return false;
        }

        // Admin and warehouse_manager can create settings
        return $user->hasRole(Role::ADMIN) || $user->hasRole(Role::WAREHOUSE_MANAGER);
    }

    /**
     * Check if the user can update services.
     *
     * @param AuthUser $user
     * @param mixed $service
     * @return bool
     */
    public function canUpdate(AuthUser $user, $service = null): bool
    {
        // Check if the user has the settings.update permission
        if (!$this->permissionService->hasPermission($user, Permission::SETTINGS_UPDATE)) {
            return false;
        }

        // Admin and warehouse_manager can update settings
        return $user->hasRole(Role::ADMIN) || $user->hasRole(Role::WAREHOUSE_MANAGER);
    }

    /**
     * Check if the user can delete services.
     *
     * @param AuthUser $user
     * @param mixed $service
     * @return bool
     */
    public function canDelete(AuthUser $user, $service = null): bool
    {
        // Check if the user has the settings.delete permission
        if (!$this->permissionService->hasPermission($user, Permission::SETTINGS_DELETE)) {
            return false;
        }

        // Admin and warehouse_manager can delete settings
        return $user->hasRole(Role::ADMIN) || $user->hasRole(Role::WAREHOUSE_MANAGER);
    }

    /**
     * Determines whether the user is authorized to view the specified service.
     *
     * @param AuthUser $user The authenticated user.
     * @param Service|string|null $service The service entity, ID, or null for general permission check.
     * @return bool True if the user can view the service, false otherwise.
     */
    public function view(AuthUser $user, $service = null): bool
    {
        return $this->canView($user, $service);
    }

    /**
     * Determines whether the user is authorized to create services.
     *
     * @param AuthUser $user The authenticated user.
     * @return bool True if the user can create services, false otherwise.
     */
    public function create(AuthUser $user): bool
    {
        return $this->canCreate($user);
    }

    /**
     * Determines whether the user is authorized to update the specified service.
     *
     * @param AuthUser $user The authenticated user.
     * @param Service|string|null $service The service entity, ID, or null for general permission check.
     * @return bool True if the user is allowed to update the service, otherwise false.
     */
    public function update(AuthUser $user, $service = null): bool
    {
        return $this->canUpdate($user, $service);
    }

    /**
     * Determines whether the user is authorized to delete the specified service.
     *
     * @param AuthUser $user The authenticated user.
     * @param Service|string|null $service The service entity, ID, or null for general permission check.
     * @return bool True if the user can delete the service, false otherwise.
     */
    public function delete(AuthUser $user, $service = null): bool
    {
        return $this->canDelete($user, $service);
    }
}
