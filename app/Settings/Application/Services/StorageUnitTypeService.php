<?php

namespace App\Settings\Application\Services;

use App\Settings\Domain\Repositories\StorageUnitTypeRepositoryInterface;
use App\Settings\Domain\Entities\StorageUnitType;
use App\Settings\Domain\ValueObjects\StorageUnitTypeCode;
use App\Core\Utils\IdGenerator;
use App\WarehouseManagement\Domain\Repositories\StorageUnitRepositoryInterface;
use App\Exceptions\BusinessRuleViolationException;

class StorageUnitTypeService
{
    private StorageUnitTypeRepositoryInterface $storageUnitTypeRepository;
    private StorageUnitRepositoryInterface $storageUnitRepository;

    public function __construct(
        StorageUnitTypeRepositoryInterface $storageUnitTypeRepository,
        StorageUnitRepositoryInterface $storageUnitRepository
    ) {
        $this->storageUnitTypeRepository = $storageUnitTypeRepository;
        $this->storageUnitRepository = $storageUnitRepository;
    }

    /**
     * Create a new storage unit type with the given details.
     *
     * @param array $data The storage unit type data containing all required fields
     * @return StorageUnitType The created storage unit type entity
     */
    public function createStorageUnitType(array $data): StorageUnitType
    {
        // Image key is now provided directly from S3 upload
        $imageKey = $data['image'] ?? null;

        $id = IdGenerator::generateUnique(fn ($id) =>
            $this->storageUnitTypeRepository->exists($id)
        );

        $storageUnitType = new StorageUnitType(
            $data['name'],
            new StorageUnitTypeCode($data['code']),
            $data['length'],
            $data['width'],
            $data['height'],
            $imageKey,
            $id
        );

        $storageUnitTypeEntity = $this->storageUnitTypeRepository->create($storageUnitType);
        return $storageUnitTypeEntity;
    }

    /**
     * Get all storage unit types.
     * 
     * @return array List of StorageUnitType entities
     */
    public function getAllStorageUnitTypes(): array
    {
        return $this->storageUnitTypeRepository->all();
    }

    /**
     * Get a specific storage unit type by its ID.
     * 
     * @param string $storageUnitTypeId The ID of the storage unit type to retrieve
     * @return StorageUnitType|null The requested storage unit type entity or null if not found
     */
    public function getStorageUnitType(string $storageUnitTypeId): ?StorageUnitType
    {
        return $this->storageUnitTypeRepository->findById($storageUnitTypeId);
    }

    /**
     * Delete a storage unit type by its ID.
     *
     * @param string $id The ID of the storage unit type to delete
     * @return bool True if deleted successfully, false if not found
     * @throws BusinessRuleViolationException If the storage unit type is referenced by existing storage units
     */
    public function deleteStorageUnitType($id): bool
    {
        // Check if the storage unit type exists
        if (!$this->storageUnitTypeRepository->exists($id)) {
            return false;
        }

        // Check if any storage units are using this type
        if ($this->storageUnitRepository->existsByTypeId($id)) {
            throw new BusinessRuleViolationException('Cannot delete storage unit type because it is being used by existing storage units');
        }

        // TODO: Add S3 image deletion when S3 upload is implemented
        return $this->storageUnitTypeRepository->delete($id);
    }

    /**
     * Update a storage unit type with the given details.
     *
     * @param string $id The ID of the storage unit type to update
     * @param array $attributes The attributes to update
     * @return StorageUnitType|null The updated storage unit type entity or null if not found
     */
    public function updateStorageUnitType(string $id, array $attributes): ?StorageUnitType
    {
        // TODO: Add S3 image replacement logic when S3 upload is implemented
        return $this->storageUnitTypeRepository->update($id, $attributes);
    }

    /**
     * Get all available storage unit type codes.
     *
     * @return array<string> List of all valid storage unit type codes
     */
    public function getStorageUnitTypeCodes(): array
    {
        return StorageUnitTypeCode::getAllCodes();
    }
}
