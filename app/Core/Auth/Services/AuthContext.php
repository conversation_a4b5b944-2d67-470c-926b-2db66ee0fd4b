<?php

namespace App\Core\Auth\Services;

use App\Core\Auth\Models\AuthUser;
use App\Core\Auth\ValueObjects\Role;

/**
 * AuthContext service for accessing the current authenticated user.
 */
class AuthContext
{
    /**
     * The current authenticated user.
     *
     * @var AuthUser|null
     */
    private ?AuthUser $user = null;

    /**
     * Set the current authenticated user.
     *
     * @param AuthUser $user
     * @return void
     */
    public function setUser(AuthUser $user): void
    {
        $this->user = $user;
    }

    /**
     * Get the current authenticated user.
     *
     * @return AuthUser|null
     */
    public function getUser(): ?AuthUser
    {
        return $this->user;
    }

    /**
     * Check if a user is authenticated.
     *
     * @return bool
     */
    public function isAuthenticated(): bool
    {
        return $this->user !== null;
    }

    /**
     * Check if the current user has a specific role.
     *
     * @param Role|string $role
     * @return bool
     */
    public function hasRole(Role|string $role): bool
    {
        if (!$this->isAuthenticated()) {
            return false;
        }

        return $this->user->hasRole($role);
    }

    /**
     * Check if the current user has any of the given roles.
     *
     * @param array $roles
     * @return bool
     */
    public function hasAnyRole(array $roles): bool
    {
        if (!$this->isAuthenticated()) {
            return false;
        }

        return $this->user->hasAnyRole($roles);
    }

    /**
     * Check if the current user has all of the given roles.
     *
     * @param array $roles
     * @return bool
     */
    public function hasAllRoles(array $roles): bool
    {
        if (!$this->isAuthenticated()) {
            return false;
        }

        return $this->user->hasAllRoles($roles);
    }

    /**
     * Get the user ID of the current authenticated user.
     *
     * @return int|null
     */
    public function getUserId(): ?int
    {
        if (!$this->isAuthenticated()) {
            return null;
        }

        return $this->user->getId();
    }

    /**
     * Clear the current authenticated user.
     *
     * @return void
     */
    public function clear(): void
    {
        $this->user = null;
    }
}
