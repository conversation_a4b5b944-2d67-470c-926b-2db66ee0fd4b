<?php

namespace App\Core\Auth\Services;

use App\Core\Auth\Models\AuthUser;
use App\Core\Auth\ValueObjects\Role;

/**
 * Service for handling permission checks based on roles.
 *
 * This service reads permission configuration from config/permissions.php
 * and provides methods to check user permissions based on their roles.
 */
class PermissionService
{

    /**
     * Retrieves the mapping of roles to their assigned permissions from configuration.
     *
     * @return array<string, array<string>> Associative array where keys are role names and values are arrays of permission strings.
     */
    private function getAllRolePermissions(): array
    {
        return config('permissions.role_permissions', []);
    }

    /**
     * Determines whether the user has the specified permission based on their roles.
     *
     * Returns true if the user has the admin role or if any of the user's roles grant the given permission; otherwise, returns false.
     *
     * @param AuthUser $user The user whose permissions are being checked.
     * @param string $permission The permission to verify.
     * @return bool True if the user has the permission; false otherwise.
     */
    public function hasPermission(AuthUser $user, string $permission): bool
    {
        $rolePermissions = $this->getAllRolePermissions();

        // Admin has all permissions
        if ($user->hasRole(Role::ADMIN)) {
            return true;
        }

        // Check if any of the user's roles grant this permission
        foreach ($user->getRoles() as $role) {
            $roleName = $role->getName();
            if (isset($rolePermissions[$roleName]) && in_array($permission, $rolePermissions[$roleName])) {
                return true;
            }
        }

        return false;
    }

    /**
     * Determines if the user has at least one permission from the provided list.
     *
     * @param AuthUser $user The user whose permissions are being checked.
     * @param array $permissions List of permission strings to check.
     * @return bool True if the user has any of the specified permissions; false otherwise.
     */
    public function hasAnyPermission(AuthUser $user, array $permissions): bool
    {
        foreach ($permissions as $permission) {
            if ($this->hasPermission($user, $permission)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Determines whether the user has all specified permissions.
     *
     * Returns true only if the user possesses every permission in the provided list; otherwise, returns false.
     *
     * @param AuthUser $user The user whose permissions are being checked.
     * @param array $permissions List of permission strings to verify.
     * @return bool True if the user has all permissions; false otherwise.
     */
    public function hasAllPermissions(AuthUser $user, array $permissions): bool
    {
        foreach ($permissions as $permission) {
            if (!$this->hasPermission($user, $permission)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Returns all permissions granted to the specified user.
     *
     * If the user has the admin role, all permissions from every role are returned. Otherwise, the method aggregates and returns a unique list of permissions from all roles assigned to the user.
     *
     * @param AuthUser $user The user whose permissions are being retrieved.
     * @return array List of unique permissions assigned to the user.
     */
    public function getUserPermissions(AuthUser $user): array
    {
        $rolePermissions = $this->getAllRolePermissions();

        // Admin has all permissions
        if ($user->hasRole(Role::ADMIN)) {
            $allRolePermissions = array_values($rolePermissions);
            return $allRolePermissions ? array_unique(array_merge(...$allRolePermissions)) : [];
        }

        $permissions = [];
        foreach ($user->getRoles() as $role) {
            $roleName = $role->getName();
            if (isset($rolePermissions[$roleName])) {
                $permissions = array_merge($permissions, $rolePermissions[$roleName]);
            }
        }

        return array_unique($permissions);
    }

    /**
     * Retrieves a unique list of all permissions defined in the configuration.
     *
     * @return array<string> List of all available permissions.
     */
    public function getAllPermissions(): array
    {
        $permissions = config('permissions.permissions', []);
        $allPermissions = [];

        foreach ($permissions as $modulePermissions) {
            $allPermissions = array_merge($allPermissions, array_values($modulePermissions));
        }

        return array_unique($allPermissions);
    }

    /**
     * Returns the list of permissions assigned to the specified role.
     *
     * If the role does not exist in the configuration, an empty array is returned.
     *
     * @param string $roleName Name of the role to retrieve permissions for.
     * @return array<string> List of permissions associated with the role.
     */
    public function getRolePermissions(string $roleName): array
    {
        $rolePermissions = $this->getAllRolePermissions();
        return $rolePermissions[$roleName] ?? [];
    }

    /**
     * Determines whether a given permission is defined in the system.
     *
     * @param string $permission The permission to check.
     * @return bool True if the permission exists; otherwise, false.
     */
    public function permissionExists(string $permission): bool
    {
        return in_array($permission, $this->getAllPermissions());
    }
}
