<?php

namespace App\Core\Auth\Guards;

use App\Core\Auth\Services\AuthContext;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Contracts\Auth\Guard;

/**
 * Custom JWT Guard that bridges our AuthContext with <PERSON><PERSON>'s authorization system.
 */
class JwtGuard implements Guard
{
    /**
     * The auth context service.
     *
     * @var AuthContext
     */
    protected AuthContext $authContext;

    /**
     * Initializes the JwtGuard with the provided authentication context.
     *
     * @param AuthContext $authContext Authentication context used to manage user state.
     */
    public function __construct(AuthContext $authContext)
    {
        $this->authContext = $authContext;
    }

    /**
     * Returns true if a user is currently authenticated.
     *
     * Delegates authentication status check to the underlying AuthContext service.
     *
     * @return bool True if the user is authenticated, false otherwise.
     */
    public function check(): bool
    {
        return $this->authContext->isAuthenticated();
    }

    /**
     * Checks if there is no authenticated user.
     *
     * @return bool True if the current user is not authenticated; otherwise, false.
     */
    public function guest(): bool
    {
        return !$this->check();
    }

    /**
     * Returns the currently authenticated user, or null if no user is authenticated.
     *
     * @return \Illuminate\Contracts\Auth\Authenticatable|null The authenticated user instance or null.
     */
    public function user(): ?Authenticatable
    {
        return $this->authContext->getUser();
    }

    /**
     * Returns the ID of the currently authenticated user.
     *
     * @return int|string|null The user ID, or null if no user is authenticated.
     */
    public function id()
    {
        return $this->authContext->getUserId();
    }

    /**
     * Always returns false, as credential validation is not supported by this JWT guard.
     *
     * @param array $credentials Ignored credentials array.
     * @return bool Always false.
     */
    public function validate(array $credentials = []): bool
    {
        // Not implemented for JWT guard
        return false;
    }

    /**
     * Checks whether an authenticated user is present in the guard.
     *
     * @return bool True if a user is authenticated; otherwise, false.
     */
    public function hasUser(): bool
    {
        return $this->authContext->isAuthenticated();
    }

    /****
     * Sets the current authenticated user in the authentication context.
     *
     * @param \Illuminate\Contracts\Auth\Authenticatable $user The user instance to set as authenticated.
     * @return static The guard instance for method chaining.
     */
    public function setUser(Authenticatable $user): static
    {
        $this->authContext->setUser($user);
        return $this;
    }
}
