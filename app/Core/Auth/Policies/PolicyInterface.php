<?php

namespace App\Core\Auth\Policies;

use App\Core\Auth\Models\AuthUser;

/**
 * Interface for all policy objects in the system.
 */
interface PolicyInterface
{
    /**
     * Check if the user can view the resource.
     *
     * @param AuthUser $user
     * @param mixed $resource
     * @return bool
     */
    public function canView(AuthUser $user, $resource): bool;

    /**
     * Check if the user can create the resource.
     *
     * @param AuthUser $user
     * @return bool
     */
    public function canCreate(AuthUser $user): bool;

    /**
     * Check if the user can update the resource.
     *
     * @param AuthUser $user
     * @param mixed $resource
     * @return bool
     */
    public function canUpdate(AuthUser $user, $resource): bool;

    /**
     * Check if the user can delete the resource.
     *
     * @param AuthUser $user
     * @param mixed $resource
     * @return bool
     */
    public function canDelete(AuthUser $user, $resource): bool;
}
