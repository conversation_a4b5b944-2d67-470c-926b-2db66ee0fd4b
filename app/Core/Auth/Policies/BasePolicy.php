<?php

namespace App\Core\Auth\Policies;

use App\Core\Auth\Models\AuthUser;
use App\Core\Auth\ValueObjects\Role;

/**
 * Base policy implementation for all policies in the system.
 */
abstract class BasePolicy implements PolicyInterface
{
    /**
     * Check if the user has admin role.
     *
     * @param AuthUser $user
     * @return bool
     */
    protected function isAdmin(AuthUser $user): bool
    {
        return $user->hasRole(Role::ADMIN);
    }

    /**
     * Check if the user has warehouse manager role.
     *
     * @param AuthUser $user
     * @return bool
     */
    protected function isWarehouseManager(AuthUser $user): bool
    {
        return $user->hasRole(Role::WAREHOUSE_MANAGER);
    }

    /**
     * Check if the user has stock controller role.
     *
     * @param AuthUser $user
     * @return bool
     */
    protected function isStockController(AuthUser $user): bool
    {
        return $user->hasRole(Role::STOCK_CONTROLLER);
    }

    /**
     * Check if the user has fulfillment officer role.
     *
     * @param AuthUser $user
     * @return bool
     */
    protected function isFulfillmentOfficer(AuthUser $user): bool
    {
        return $user->hasRole(Role::FULFILLMENT_OFFICER);
    }

    /**
     * Check if the user has any of the given roles.
     *
     * @param AuthUser $user
     * @param array $roles
     * @return bool
     */
    protected function hasAnyRole(AuthUser $user, array $roles): bool
    {
        return $user->hasAnyRole($roles);
    }

    /**
     * Check if the user has all of the given roles.
     *
     * @param AuthUser $user
     * @param array $roles
     * @return bool
     */
    protected function hasAllRoles(AuthUser $user, array $roles): bool
    {
        return $user->hasAllRoles($roles);
    }
}
