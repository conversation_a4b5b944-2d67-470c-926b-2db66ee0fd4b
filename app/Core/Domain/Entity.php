<?php

namespace App\Core\Domain;
use DomainException;
/**
 * Base entity class for all domain entities.
 */
abstract class Entity
{
    /**
     * The entity's ID.
     *
     * @var mixed
     */
    protected $id;

    /**
     * Create a new entity instance.
     *
     * @param mixed|null $id
     */
    public function __construct($id = null)
    {
        if ($id !== null) {
            $this->id = $id;
        }
    }

    /**
     * Get the entity's ID.
     *
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set the entity's ID.
     *
     * @param mixed $id
     * @return self
     */
    protected function setId($id): self
    {
        if ($this->id !== null) {
            throw new DomainException("Cannot change ID of existing entity");
        }
        $this->id = $id;
        return $this;
    }
}
