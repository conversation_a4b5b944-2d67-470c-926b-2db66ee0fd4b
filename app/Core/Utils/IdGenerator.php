<?php

namespace App\Core\Utils;

use Illuminate\Support\Str;

/**
 * Utility class for generating unique identifiers across all domains.
 */
class IdGenerator {

    /**
     * Generate a unique ID that does not already exist in the system.
     *
     * @param callable $exists A function that checks if the ID exists
     * @param int $length The length of the ID to generate (default: 8)
     * @return string The generated unique ID
     */
    public static function generateUnique(callable $exists, int $length = 8): string {
        do {
            $id = self::generateId($length);
        } while ($exists($id));

        return $id;
    }
    
    /**
     * Generate a unique ID of specified length based on UUID.
     *
     * @param int $length The length of the ID to generate (default: 8)
     * @return string The generated unique ID
     */
    public static function generateId(int $length = 8): string {
        return substr(str_replace('-', '', self::generateUuid()), 0, $length);
    }

    /**
     * Generate a unique ID with a specific prefix.
     *
     * @param string $prefix The prefix to add to the ID
     * @param int $length The length of the ID part (without prefix)
     * @return string The generated unique ID with prefix
     */
    public static function generateWithPrefix(string $prefix, int $length = 8): string {
        return $prefix . self::generateId($length);
    }

    /**
     * Generate a full UUID.
     *
     * @return string The generated UUID
     */
    public static function generateUuid(): string {
        return Str::uuid()->toString();
    }
}
