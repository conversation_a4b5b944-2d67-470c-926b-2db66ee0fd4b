<?php

namespace App\Core\Interfaces\Http\Resources\FLM;

use Illuminate\Http\Resources\Json\JsonResource;

class AreaResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @OA\Schema(
     *     schema="AreaResource",
     *     @OA\Property(property="id", type="integer", format="int64", example="1"),
     *     @OA\Property(property="name", type="string", example="Beirut")
     * )
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->resource['id'] ?? null,
            'name' => $this->resource['name'] ?? null
        ];
    }
}
