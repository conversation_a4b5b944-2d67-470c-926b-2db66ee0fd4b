<?php

namespace App\Core\Interfaces\Http\Controllers\FLM;

use App\Core\Contracts\FLM\AreaServiceInterface;
use App\Core\Interfaces\Http\Resources\FLM\AreaResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;

class AreaAPIController extends Controller
{
    private AreaServiceInterface $areaService;

    public function __construct(AreaServiceInterface $areaService)
    {
        $this->areaService = $areaService;
    }

    /**
     * Get areas from FLM
     * 
     * @OA\Get(
     *     path="/api/areas",
     *     tags={"FLM"},
     *     summary="Get areas from FLM",
     *     description="Returns a list of areas",
     *     operationId="getAreas",
     *     @OA\Parameter(
     *         name="name",
     *         in="query",
     *         description="Filter areas by name",
     *         required=false,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="data", type="array", @OA\Items(ref="#/components/schemas/AreaResource")),
     *             @OA\Property(property="message", type="string", example="Areas retrieved successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(ref="#/components/schemas/UnauthorizedResponse")
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Internal Server Error",
     *         @OA\JsonContent(ref="#/components/schemas/InternalServerErrorResponse")
     *     ),
     * )
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $name = $request->query('name');
        $areas = $this->areaService->getAreas($name);

        return AreaResource::collection($areas)
            ->additional([
                'message' => 'Areas retrieved successfully'
            ])
            ->response();
    }
}
