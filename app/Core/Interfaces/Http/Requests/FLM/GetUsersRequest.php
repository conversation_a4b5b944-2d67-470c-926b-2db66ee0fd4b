<?php

namespace App\Core\Interfaces\Http\Requests\FLM;

use App\Http\Requests\APIBaseRequest;
use App\Core\Auth\ValueObjects\Role;

class GetUsersRequest extends APIBaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'roles' => ['nullable', 'string', function ($attribute, $value, $fail) {
                $roles = explode(',', $value);
                $trimmedRoles = array_map('trim', $roles); 
                $invalid = array_filter($trimmedRoles, fn($role) => !Role::isValid($role));

                if (!empty($invalid)) {
                    $fail('The following roles are invalid: ' . implode(', ', $invalid));
                }
            }],

        ];
    }
}
