<?php

namespace App\Core\Contracts;

/**
 * Base repository interface for all repositories in the system.
 */
interface RepositoryInterface
{
    /**
     * Find an entity by its ID.
     *
     * @param int|string $id
     * @return mixed
     */
    public function findById($id);

    /**
     * Get all entities.
     *
     * @return array
     */
    public function all();

    /**
     * Create a new entity.
     *
     * @param mixed $entity
     * @return mixed
     */
    public function create($entity);

    /**
     * Update an entity.
     *
     * @param int|string $id
     * @param array $attributes
     * @return mixed
     */
    public function update($id, $attributes);

    /**
     * Delete an entity.
     *
     * @param int|string $id
     * @return mixed
     */
    public function delete($id);

    /**
     * Check if an entity with the given ID exists.
     *
     * @param int|string $id
     * @return bool
     */
    public function exists($id): bool;
}
