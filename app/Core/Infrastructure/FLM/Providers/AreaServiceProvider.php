<?php

namespace App\Core\Infrastructure\FLM\Providers;

use Illuminate\Support\ServiceProvider;
use GuzzleHttp\Client;
use App\Core\Contracts\FLM\AreaServiceInterface;
use App\Core\Infrastructure\FLM\Services\AreaService;


class AreaServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->bind(AreaServiceInterface::class, function ($app) {
            return new AreaService(new Client([
                'timeout' => 30,
                'connect_timeout' => 5,
            ]));
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {

    }
}