<?php

namespace App\Core\Infrastructure\FLM\Providers;

use Illuminate\Support\ServiceProvider;
use App\Core\Contracts\FLM\UserServiceInterface;
use App\Core\Infrastructure\FLM\Services\UserService;
use GuzzleHttp\Client;

class UserServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->bind(UserServiceInterface::class, function ($app) {
            return new UserService(new Client([
                'timeout' => 30,
                'connect_timeout' => 5,
            ]));
        });
        
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {

    }
}