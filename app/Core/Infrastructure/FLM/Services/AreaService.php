<?php

namespace App\Core\Infrastructure\FLM\Services;

use App\Core\Contracts\FLM\AreaServiceInterface;
use App\Core\Infrastructure\FLM\Services\BaseApiService;
use GuzzleHttp\Client;

class AreaService extends BaseApiService implements AreaServiceInterface
{
    public function __construct(Client $client)
    {
        parent::__construct($client);
    }
    /**
     * Get areas from the FLM
     * @param string|null $name Optional filter for area names
     * @return array
     */
    public function getAreas(?string $name = null): array
    {
        $options = [
            'query' => []
        ];

        if ($name !== null) {
            $options['query']['name'] = $name;
        }
        return $this->makeRequest('GET', 'api/areas/names', $options);
    }
}
