<?php

namespace App\Core\Infrastructure\FLM\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpKernel\Exception\HttpException;

abstract class BaseApiService
{
    protected string $baseUrl;
    protected Client $client;

    public function __construct(Client $client)
    {
        $this->baseUrl = config('services.wakilni.api_url');
        $this->client = $client;
    }

    /**
     * Make an HTTP request to the external API
     * 
     * @param string $method HTTP method (GET, POST, PUT, DELETE, etc.)
     * @param string $endpoint API endpoint (without base URL)
     * @param array $options Guzzle request options
     * @return array Response data as array
     */
    protected function makeRequest(string $method, string $endpoint, array $options = []): array
    {
        try {
            
            $url = "{$this->baseUrl}/{$endpoint}";
            // Ensure headers contain Accept: application/json
            if (!isset($options['headers'])) {
                $options['headers'] = [];
            }

            if (!isset($options['headers']['Accept'])) {
                $options['headers']['Accept'] = 'application/json';
            }

            $response = $this->client->request($method, $url, $options);
            $body = $response->getBody()->getContents();
            $data = json_decode($body, true);
            return $data['data'] ?? [];
        } catch (RequestException $e) {
            $status = $e->getResponse()?->getStatusCode() ?? 500;
            Log::error("Failed to {$method} {$endpoint}", [
                'status' => $status,
                'message' => $e->getMessage()
            ]);
            throw new HttpException($status, "An unexpected error occurred");
        } catch (\Exception $e) {
            Log::error("Unexpected exception during {$method} {$endpoint}", [
                'message' => $e->getMessage()
            ]);

            throw new HttpException(500, "An unexpected error occurred");
        }
    }
}
