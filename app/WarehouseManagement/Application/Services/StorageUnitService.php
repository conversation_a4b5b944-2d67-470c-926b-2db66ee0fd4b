<?php

namespace App\WarehouseManagement\Application\Services;

use App\WarehouseManagement\Domain\Repositories\StorageUnitRepositoryInterface;
use App\WarehouseManagement\Domain\Entities\StorageUnit;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use App\WarehouseManagement\Application\Services\WarehouseService;
use App\Core\Utils\IdGenerator;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class StorageUnitService {
    private StorageUnitRepositoryInterface $storageUnitRepository;
    private WarehouseService $warehouseService;

    public function __construct(
        StorageUnitRepositoryInterface $storageUnitRepository,
        WarehouseService $warehouseService
    ) {
        $this->storageUnitRepository = $storageUnitRepository;
        $this->warehouseService = $warehouseService;
    }

    /**
     * Create new storage units based on an array of definitions.
     *
     * @param array $data Array of storage unit definitions, each containing warehouse_id, type_id, and quantity
     * @return array Array of created StorageUnit DTOs
     * @throws NotFoundHttpException if the warehouse does not exist
     */
    public function createStorageUnit(array $data): array {
        $warehouseId = $data['warehouse_id'];
        $warehouse = $this->warehouseService->getWarehouse($warehouseId);
        if (!$warehouse) {
            throw new NotFoundHttpException("Warehouse with ID {$warehouseId} does not exist");
        }
        $storageUnits = $data['storageUnits'];
        $createdUnits = [];
        foreach ($storageUnits as $unit) {
            $quantity = $unit['quantity'];
            foreach (range(1, $quantity) as $_) {
                $id = IdGenerator::generateUnique(fn ($id) =>
                    $this->storageUnitRepository->exists($id)
                );

                $storageUnit = new StorageUnit($warehouseId, $unit['type_id'], null, $id);
                $createdUnits[] = $this->storageUnitRepository->create($storageUnit);
            }

        }

        return $createdUnits;
    }

    public function paginateStorageUnits(int $perPage, int $page, string $warehouseId, ?string $typeId = null): LengthAwarePaginator
    {
        return $this->storageUnitRepository->paginate($perPage, $page, $warehouseId, $typeId);
    }
}
