<?php

namespace App\WarehouseManagement\Application\DTOs;

use App\WarehouseManagement\Domain\Entities\Warehouse;

class WarehouseDTO
{
    public readonly Warehouse $warehouse;
    public readonly array $storageUnitDTOs;

    public function __construct(Warehouse $warehouse, array $storageUnitDTOs = [])
    {
        $this->warehouse = $warehouse;
        $this->storageUnitDTOs = $storageUnitDTOs;
    }

    public function getWarehouse(): Warehouse
    {
        return $this->warehouse;
    }

    public function getStorageUnitDTOs(): array
    {
        return $this->storageUnitDTOs;
    }

    public function getStorageUnitCount(): int
    {
        return count($this->storageUnitDTOs);
    }
}