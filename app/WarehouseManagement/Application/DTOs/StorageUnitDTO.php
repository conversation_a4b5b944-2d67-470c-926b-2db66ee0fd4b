<?php

namespace App\WarehouseManagement\Application\DTOs;

use App\WarehouseManagement\Domain\Entities\StorageUnit;
use App\Settings\Domain\Entities\StorageUnitType;

class StorageUnitDTO
{
    public readonly StorageUnit $storageUnit;
    public readonly StorageUnitType $type;

    public function __construct(StorageUnit $storageUnit, StorageUnitType $type)
    {
        $this->storageUnit = $storageUnit;
        $this->type = $type;
    }
}
