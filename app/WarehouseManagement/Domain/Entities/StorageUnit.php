<?php

namespace App\WarehouseManagement\Domain\Entities;

use DomainException;
use App\Core\Domain\Entity;

// StorageUnit Entity
class StorageUnit extends Entity {
    private string $warehouseId;
    private string $typeId;
    private ?string $createdAt;

    public function __construct(
        string $warehouseId,
        string $typeId,
        ?string $createdAt = null,
        ?string $id = null
    ) {
        $this->setId($id);
        $this->warehouseId = $warehouseId;
        $this->typeId = $typeId;
        $this->createdAt = $createdAt;
    }

    public function getWarehouseId(): string {
        return $this->warehouseId;
    }

    public function getTypeId(): string {
        return $this->typeId;
    }

    public function getCreatedAt(): ?string {
        return $this->createdAt;
    }
}
