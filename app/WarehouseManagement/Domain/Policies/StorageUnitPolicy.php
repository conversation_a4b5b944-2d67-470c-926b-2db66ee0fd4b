<?php

namespace App\WarehouseManagement\Domain\Policies;

use App\Core\Auth\Constants\Permission;
use App\Core\Auth\Models\AuthUser;
use App\Core\Auth\Policies\BasePolicy;
use App\Core\Auth\Services\PermissionService;
use App\Core\Auth\ValueObjects\Role;
use App\WarehouseManagement\Domain\Entities\StorageUnit;

/**
 * Policy for storage unit-related operations.
 */
class StorageUnitPolicy extends BasePolicy {
    /**
     * The permission service.
     *
     * @var PermissionService
     */
    protected PermissionService $permissionService;

    /**
     * Initializes the StorageUnitPolicy with a PermissionService for permission checks.
     *
     * @param PermissionService $permissionService Service used to verify user permissions.
     */
    public function __construct(PermissionService $permissionService) {
        $this->permissionService = $permissionService;
    }

    /**
     * Determines whether the user is authorized to view the specified storage unit.
     *
     * Delegates to the `view` method for permission checks.
     *
     * @param AuthUser $user The authenticated user.
     * @param StorageUnit $storageUnit The storage unit to check access for.
     * @return bool True if the user can view the storage unit, false otherwise.
     */
    public function canView(AuthUser $user, $storageUnit): bool {
        return $this->view($user, $storageUnit);
    }

    /**
     * Determines whether the user is authorized to view the specified storage unit.
     *
     * Grants access if the user has the `storageUnit.view` permission. Users with the `ADMIN` role are always authorized. For non-admin users, access is currently granted if they have the permission, regardless of storage unit ownership.
     *
     * @param AuthUser $user The authenticated user.
     * @param StorageUnit $storageUnit The storage unit to check access for.
     * @return bool True if the user can view the storage unit, false otherwise.
     */
    public function view(AuthUser $user, $storageUnit): bool {
        // Check if the user has the storageUnit.view permission
        if (!$this->permissionService->hasPermission($user, Permission::STORAGE_UNIT_VIEW)) {
            return false;
        }

        // Admin can view any storage unit
        if ($user->hasRole(Role::ADMIN)) {
            return true;
        }

        // For non-admin users, check if they have access to this specific storage unit
        // TODO: implement storage unit ownership check
        // Currently any user with storageUnit.view permission can view any storage unit
        return true;
    }

    /**
     * Determines whether the user is authorized to create a storage unit.
     *
     * @param AuthUser $user The authenticated user.
     * @return bool True if the user has permission to create a storage unit; otherwise, false.
     */
    public function canCreate(AuthUser $user): bool {
        return $this->create($user);
    }

    /**
     * Determines whether the user has permission to create a storage unit.
     *
     * @param AuthUser $user The authenticated user.
     * @return bool True if the user has the `storageUnit.create` permission; otherwise, false.
     */
    public function create(AuthUser $user): bool {
        // Check if the user has the storageUnit.create permission
        return $this->permissionService->hasPermission($user, Permission::STORAGE_UNIT_CREATE);
    }

    /**
     * Determines whether the user is authorized to update the specified storage unit.
     *
     * Delegates to the `update` method for permission checks.
     *
     * @param AuthUser $user The authenticated user.
     * @param StorageUnit $storageUnit The storage unit to be updated.
     * @return bool True if the user can update the storage unit, false otherwise.
     */
    public function canUpdate(AuthUser $user, $storageUnit): bool {
        return $this->update($user, $storageUnit);
    }

    /**
     * Determines whether the user is authorized to update the specified storage unit.
     *
     * Grants access if the user has the `storageUnit.update` permission. Users with the `ADMIN` role are always authorized. Ownership checks are not currently enforced; any user with the permission can update any storage unit.
     *
     * @param AuthUser $user The authenticated user.
     * @param StorageUnit $storageUnit The storage unit to be updated.
     * @return bool True if the user is allowed to update the storage unit, otherwise false.
     */
    public function update(AuthUser $user, $storageUnit): bool {
        // Check if the user has the storageUnit.update permission
        if (!$this->permissionService->hasPermission($user, Permission::STORAGE_UNIT_UPDATE)) {
            return false;
        }

        // Admin can update any storage unit
        if ($user->hasRole(Role::ADMIN)) {
            return true;
        }

        // For non-admin users, check if they have access to this specific storage unit
        // TODO: implement storage unit ownership check
        // Currently any user with storageUnit.update permission can update any storage unit
        return true;
    }

    /**
     * Determines whether the user is authorized to delete the specified storage unit.
     *
     * @param AuthUser $user The user requesting the action.
     * @param StorageUnit $storageUnit The storage unit to be deleted.
     * @return bool True if the user has permission to delete the storage unit; otherwise, false.
     */
    public function canDelete(AuthUser $user, $storageUnit): bool {
        return $this->delete($user, $storageUnit);
    }

    /**
     * Determines whether the user has permission to delete the specified storage unit.
     *
     * @return bool True if the user has the `storageUnit.delete` permission; otherwise, false.
     */
    public function delete(AuthUser $user, $storageUnit): bool {
        // Check if the user has the storageUnit.delete permission
        if (!$this->permissionService->hasPermission($user, Permission::STORAGE_UNIT_DELETE)) {
            return false;
        }

        // Admin can delete any storage unit
        if ($user->hasRole(Role::ADMIN)) {
            return true;
        }

        // For non-admin users, implement ownership check
        // TODO: implement storage unit ownership check
        return true;
    }
}
