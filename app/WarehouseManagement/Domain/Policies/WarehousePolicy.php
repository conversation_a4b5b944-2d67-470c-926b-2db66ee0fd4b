<?php

namespace App\WarehouseManagement\Domain\Policies;

use App\Core\Auth\Constants\Permission;
use App\Core\Auth\Models\AuthUser;
use App\Core\Auth\Policies\BasePolicy;
use App\Core\Auth\Services\PermissionService;
use App\Core\Auth\ValueObjects\Role;
use App\WarehouseManagement\Domain\Models\Warehouse;

/**
 * Policy for warehouse-related operations.
 */
class WarehousePolicy extends BasePolicy
{
    /**
     * The permission service.
     *
     * @var PermissionService
     */
    protected PermissionService $permissionService;

    /****
     * Initializes the WarehousePolicy with a PermissionService for permission checks.
     *
     * @param PermissionService $permissionService Service used to verify user permissions.
     */
    public function __construct(PermissionService $permissionService)
    {
        $this->permissionService = $permissionService;
    }

    /**
     * Determines whether the user is authorized to view the specified warehouse.
     *
     * Delegates to the `view` method for permission checks.
     *
     * @param AuthUser $user The authenticated user.
     * @param Warehouse $warehouse The warehouse to check access for.
     * @return bool True if the user can view the warehouse, false otherwise.
     */
    public function canView(AuthUser $user, $warehouse): bool
    {
        return $this->view($user, $warehouse);
    }

    /**
     * Determines whether the user is authorized to view the specified warehouse.
     *
     * @param AuthUser $user The authenticated user.
     * @param Warehouse|string|null $warehouse The warehouse entity, warehouse ID, or null for general permission check.
     * @return bool True if the user can view the warehouse, false otherwise.
     */
    public function view(AuthUser $user, $warehouse = null): bool
    {
        // Check if the user has the warehouse.view permission
        if (!$this->permissionService->hasPermission($user, Permission::WAREHOUSE_VIEW)) {
            return false;
        }

        // Admin can view any warehouse
        if ($user->hasRole(Role::ADMIN)) {
            return true;
        }

        // TODO: implement warehouse ownership check

        return true;
    }

    /**
     * Determines whether the user is authorized to create a warehouse.
     *
     * @param AuthUser $user The authenticated user.
     * @return bool True if the user has permission to create a warehouse; otherwise, false.
     */
    public function canCreate(AuthUser $user): bool
    {
        return $this->create($user);
    }

    /**
     * Determines whether the user has permission to create a warehouse.
     *
     * @param AuthUser $user The authenticated user.
     * @return bool True if the user has the `warehouse.create` permission; otherwise, false.
     */
    public function create(AuthUser $user): bool
    {
        // Check if the user has the warehouse.create permission
        return $this->permissionService->hasPermission($user, Permission::WAREHOUSE_CREATE);
    }


    /**
     * Determines whether the user is authorized to update the specified warehouse.
     *
     * Delegates to the `update` method for permission checks.
     *
     * @param AuthUser $user The authenticated user.
     * @param Warehouse $warehouse The warehouse to be updated.
     * @return bool True if the user can update the warehouse, false otherwise.
     */
    public function canUpdate(AuthUser $user, $warehouse): bool
    {
        return $this->update($user, $warehouse);
    }

    /**
     * Determines whether the user is authorized to update the specified warehouse.
     *
     * @param AuthUser $user The authenticated user.
     * @param Warehouse|string|null $warehouse The warehouse entity, warehouse ID, or null for general permission check.
     * @return bool True if the user is allowed to update the warehouse, otherwise false.
     */
    public function update(AuthUser $user, $warehouse = null): bool
    {
        // Check if the user has the warehouse.update permission
        if (!$this->permissionService->hasPermission($user, Permission::WAREHOUSE_UPDATE)) {
            return false;
        }

        // Admin can update any warehouse
        if ($user->hasRole(Role::ADMIN)) {
            return true;
        }

        // TODO: implement warehouse ownership check

        return true;
    }

    /**
     * Determines whether the user is authorized to delete the specified warehouse.
     *
     * @param AuthUser $user The user requesting the action.
     * @param Warehouse $warehouse The warehouse to be deleted.
     * @return bool True if the user has permission to delete the warehouse; otherwise, false.
     */
    public function canDelete(AuthUser $user, $warehouse): bool
    {
        return $this->delete($user, $warehouse);
    }

    /**
     * Determines whether the user has permission to delete the specified warehouse.
     *
     * @param AuthUser $user The authenticated user.
     * @param Warehouse|string|null $warehouse The warehouse entity, warehouse ID, or null for general permission check.
     * @return bool True if the user has the `warehouse.delete` permission; otherwise, false.
     */
    public function delete(AuthUser $user, $warehouse = null): bool
    {
        return $this->permissionService->hasPermission($user, Permission::WAREHOUSE_DELETE);
    }
}
