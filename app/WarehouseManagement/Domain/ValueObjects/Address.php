<?php

namespace App\WarehouseManagement\Domain\ValueObjects;

use App\Core\ValueObjects\ValueObject;
use InvalidArgumentException;

class Address extends ValueObject
{
    private string $area;
    private string $building;
    private int $floor;
    private string $directions;

    public function __construct(
        string $area,
        string $building,
        int $floor,
        string $directions
    ) {
        if (empty(trim($area))) {
            throw new InvalidArgumentException("Area cannot be empty");
        }
        if (empty(trim($directions))) {
            throw new InvalidArgumentException("Directions cannot be empty");
        }
        if (empty(trim($building))) {
            throw new InvalidArgumentException("Building cannot be empty");
        }
        if ($floor < 0) {
            throw new InvalidArgumentException("Floor must be a positive number");
        }

        $this->area = $area;
        $this->building = $building;
        $this->floor = $floor;
        $this->directions = $directions;
    }

    public function getArea(): string
    {
        return $this->area;
    }

    public function getBuilding(): string
    {
        return $this->building;
    }

    public function getFloor(): int
    {
        return $this->floor;
    }

    public function getDirections(): string
    {
        return $this->directions;
    }

    public function equals(ValueObject $other): bool
    {
        if (! $other instanceof self) {
            return false;
        }

        return
            $this->area       === $other->area
            && $this->building   === $other->building
            && $this->floor      === $other->floor
            && $this->directions === $other->directions;
    }
}
