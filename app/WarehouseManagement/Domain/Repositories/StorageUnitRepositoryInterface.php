<?php

namespace App\WarehouseManagement\Domain\Repositories;

use App\Core\Contracts\RepositoryInterface;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

/**
 * Interface for storage unit repository.
 */
interface StorageUnitRepositoryInterface extends RepositoryInterface {

    public function existsByTypeId(string $typeId): bool;

    public function paginate(int $perPage, int $page, string $warehouseId, ?string $typeId = null): LengthAwarePaginator;
}
