<?php

namespace App\WarehouseManagement\Infrastructure\Persistence;

use App\WarehouseManagement\Domain\Entities\StorageUnit as StorageUnitEntity;
use App\WarehouseManagement\Domain\Repositories\StorageUnitRepositoryInterface;
use App\WarehouseManagement\Infrastructure\Models\StorageUnit as StorageUnitEloquent;
use App\WarehouseManagement\Application\DTOs\StorageUnitDTO;
use App\Settings\Domain\Entities\StorageUnitType;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use App\Settings\Domain\ValueObjects\StorageUnitTypeCode;

class EloquentStorageUnitRepository implements StorageUnitRepositoryInterface {

    private function mapToDomainEntity(StorageUnitEloquent $eloquentModel): StorageUnitEntity {
        return new StorageUnitEntity(
            $eloquentModel->warehouse_id,
            $eloquentModel->type_id,
            $eloquentModel->created_at,
            $eloquentModel->id
        );
    }

    /**
     * Create a new storage unit
     *
     * @param \App\WarehouseManagement\Domain\Entities\StorageUnit $storageUnitEntity The storage unit domain entity to persist
     * @return \App\WarehouseManagement\Application\DTOs\StorageUnitDTO The created storage unit DTO
     */
    public function create($storageUnitEntity): StorageUnitDTO {
        $storageUnit = new StorageUnitEloquent();
        // Set properties
        $storageUnit->warehouse_id = $storageUnitEntity->getWarehouseId();
        $storageUnit->type_id = $storageUnitEntity->getTypeId();
        $storageUnit->id = $storageUnitEntity->getId();
        $storageUnit->save();

        $storageUnit->load('type');

        $storageUnitEntity = new StorageUnitEntity(
            $storageUnitEntity->getWarehouseId(),
            $storageUnitEntity->getTypeId(),
            $storageUnit->created_at,
            $storageUnitEntity->getId()
        );

        $typeEntity = new StorageUnitType(
            $storageUnit->type->name,
            new StorageUnitTypeCode($storageUnit->type->code),
            $storageUnit->type->length,
            $storageUnit->type->width,
            $storageUnit->type->height,
            $storageUnit->type->image,
            $storageUnit->type->id
        );

        return new StorageUnitDTO($storageUnitEntity, $typeEntity);
    }

    /**
     * Check if a storage unit with the given ID exists
     *
     * @param string $id The ID to check
     * @return bool True if the storage unit exists, false otherwise
     */
    public function exists($id): bool {
        return StorageUnitEloquent::whereId($id)->exists();
    }

    /**
     * Check if any storage units exist with the given type ID.
     *
     * @param string $typeId The storage unit type ID to check
     * @return bool True if storage units exist with this type, false otherwise
     */
    public function existsByTypeId(string $typeId): bool {
        return StorageUnitEloquent::where('type_id', $typeId)->exists();
    }

    public function findById($id) {
    }


    /**
     * Paginate storage units for a specific warehouse.
     *
     * @param  int  $perPage
     * @param  int  $page
     * @param  int  $warehouseId
     * @param  int  $typeId
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function paginate($perPage, $page, $warehouseId, $typeId = null): LengthAwarePaginator {
        $query = StorageUnitEloquent::query()
            ->where('warehouse_id', $warehouseId);
            
        if ($typeId !== null) {
            $query->where('type_id', $typeId);
        }
        
        $paginator = $query->with('type')->paginate($perPage, ['*'], 'page', $page)
            ->through(function($item) {
                $storageUnitEntity = $this->mapToDomainEntity($item);
                $typeEntity = new StorageUnitType(
                    $item->type->name,
                    new StorageUnitTypeCode($item->type->code),
                    $item->type->length,
                    $item->type->width,
                    $item->type->height,
                    $item->type->image,
                    $item->type->id
                );
                return new StorageUnitDTO($storageUnitEntity, $typeEntity);
            });

        return $paginator;
    }

    public function all() {
    }

    public function update($id, $attributes) {
    }

    public function delete($id) {
    }
}
