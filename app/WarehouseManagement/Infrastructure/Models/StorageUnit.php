<?php

namespace App\WarehouseManagement\Infrastructure\Models;

use App\Settings\Infrastructure\Models\StorageUnitType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @OA\Schema(
 *     schema="StorageUnit",
 *     @OA\Property(property="id", type="string", example="unit001"),
 *     @OA\Property(property="warehouse_id", type="string", example="efd329se"),
 *     @OA\Property(property="type_id", type="integer", example="1")
 * )
 */
class StorageUnit extends Model {
    use HasFactory;
    use SoftDeletes;
    public $incrementing = false;
    protected $keyType = 'string';

    protected $table = 'storage_units';
    protected $fillable = [
        'id',
        'warehouse_id',
        'type_id'
    ];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     **/
    public function warehouse() {
        return $this->belongsTo(Warehouse::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     **/
    public function type() {
        return $this->belongsTo(StorageUnitType::class);
    }
}
