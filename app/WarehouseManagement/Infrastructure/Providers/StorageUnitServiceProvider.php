<?php

namespace App\WarehouseManagement\Infrastructure\Providers;

use Illuminate\Support\ServiceProvider;
use App\WarehouseManagement\Domain\Repositories\StorageUnitRepositoryInterface;
use App\WarehouseManagement\Infrastructure\Persistence\EloquentStorageUnitRepository;

class StorageUnitServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->bind(
            StorageUnitRepositoryInterface::class,
            EloquentStorageUnitRepository::class
        );
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void {}
}
