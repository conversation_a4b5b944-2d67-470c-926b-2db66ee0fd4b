<?php

namespace App\WarehouseManagement\Infrastructure\Providers;

use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Route;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;

class WarehouseRouteServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        $this->mapApiRoutes();
    }

    /**
     * Map the API routes for this bounded context.
     */
    protected function mapApiRoutes(): void
    {
        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });
        Route::middleware('api')
            ->prefix('api/warehouse')
            ->name('warehouse.')
            ->group(base_path('app/WarehouseManagement/Interfaces/Http/routes.php'));
    }
}
