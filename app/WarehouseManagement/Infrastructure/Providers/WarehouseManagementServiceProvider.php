<?php

namespace App\WarehouseManagement\Infrastructure\Providers;

use Illuminate\Support\ServiceProvider;
use App\WarehouseManagement\Domain\Repositories\WarehouseRepositoryInterface;
use App\WarehouseManagement\Infrastructure\Persistence\EloquentWarehouseRepository;

class WarehouseManagementServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->bind(
            WarehouseRepositoryInterface::class,
            EloquentWarehouseRepository::class
        );
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void {}
}
