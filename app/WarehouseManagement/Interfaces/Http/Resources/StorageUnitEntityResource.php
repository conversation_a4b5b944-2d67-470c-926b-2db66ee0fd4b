<?php

namespace App\WarehouseManagement\Interfaces\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource for StorageUnit entity (not DTO)
 */
class StorageUnitEntityResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        $entity = $this->resource;
        
        return [
            'id' => $entity->getId(),
            'warehouse_id' => $entity->getWarehouseId(),
            'type_id' => $entity->getTypeId(),
        ];
    }
}