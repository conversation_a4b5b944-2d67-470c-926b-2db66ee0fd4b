<?php

namespace App\WarehouseManagement\Interfaces\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;


/**
 * @OA\Schema(
 *     schema="WarehouseResource",
 *     @OA\Property(property="id", type="string", example="warehouse-123"),
 *     @OA\Property(property="name", type="string", example="Main Warehouse"),
 *     @OA\Property(property="manager_id", type="integer", example=100),
 *     @OA\Property(property="manager_name", type="string", example="John Doe"),
 *     @OA\Property(property="total_space", type="number", format="float", example=1500.50),
 *     @OA\Property(
 *         property="address",
 *         type="object",
 *         @OA\Property(property="area", type="string", example="Industrial Zone"),
 *         @OA\Property(property="building", type="string", example="Building A"),
 *         @OA\Property(property="floor", type="integer", example=2),
 *         @OA\Property(property="directions", type="string", example="Near the main highway")
 *     ),
 *     @OA\Property(
 *         property="storage_units",
 *         type="array",
 *         @OA\Items(ref="#/components/schemas/StorageUnitResource")
 *     ),
 *     
 * )
 */
class WarehouseResource extends JsonResource {
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request): array {
        $dto = $this->resource;
        $entity = $dto->getWarehouse();

        return [
            'id' => $entity->getId(),
            'name' => $entity->getName(),
            'manager_id' => $entity->getManagerId(),
            'manager_name' => $entity->getManagerName(),
            'total_space' => $entity->getTotalSpace(),
            'address' => [
                'area' => $entity->getAddress()->getArea(),
                'building' => $entity->getAddress()->getBuilding(),
                'floor' => $entity->getAddress()->getFloor(),
                'directions' => $entity->getAddress()->getDirections()
            ],
            'storage_units' => StorageUnitResource::collection($dto->getStorageUnitDTOs())
        ];
    }
}
