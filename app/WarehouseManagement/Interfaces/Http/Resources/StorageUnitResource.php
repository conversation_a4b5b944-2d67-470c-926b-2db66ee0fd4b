<?php

namespace App\WarehouseManagement\Interfaces\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @OA\Schema(
 *     schema="StorageUnitResource",
 *     @OA\Property(property="id", type="string", example="0001abcd"),
 *     @OA\Property(property="warehouse_id", type="string", example="efd329se"),
 *     @OA\Property(property="type_id", type="string", example="0001abcd"),
 *     @OA\Property(property="name", type="string", example="Big Shelf"),
 *     @OA\Property(property="barcode", type="string", example="BS-0001abcd"),
 *     @OA\Property(property="created_at", type="string", format="date", example="15-12-2024", description="Creation date in d-m-Y format")
 * )
 */
class StorageUnitResource extends JsonResource {
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request): array {
        $dto = $this->resource;

        return [
            'id' => $dto->storageUnit->getId(),
            'warehouse_id' => $dto->storageUnit->getWarehouseId(),
            'type_id' => $dto->storageUnit->getTypeId(),
            'name' => $dto->type->getName(),
            'created_at' => $dto->storageUnit->getCreatedAt(),
            'barcode' => $dto->type->getCode()->getCode() . '-' . $dto->storageUnit->getId()
        ];
    }
}
