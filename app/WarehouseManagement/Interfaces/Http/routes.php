<?php

use App\WarehouseManagement\Interfaces\Http\Controllers\WarehouseAPIController;
use Illuminate\Support\Facades\Route;
use App\WarehouseManagement\Interfaces\Http\Controllers\StorageUnitAPIController;

Route::middleware('jwt.verify')->group(function () {
        Route::get('/', [WarehouseAPIController::class, 'index']);
        Route::get('{id}', [WarehouseAPIController::class, 'show']);
        Route::put('{id}', [WarehouseAPIController::class, 'update']);
        Route::post('/', [WarehouseAPIController::class, 'store']);
        Route::delete('{id}', [WarehouseAPIController::class, 'destroy']);
        Route::post('/{warehouse_id}/storage-units', [StorageUnitAPIController::class, 'store']);
        Route::get('/{warehouse_id}/storage-units', [StorageUnitAPIController::class, 'index']);
});
