<?php

namespace App\WarehouseManagement\Interfaces\Http\Requests;

use App\Http\Requests\APIBaseRequest;
use App\WarehouseManagement\Domain\Policies\StorageUnitPolicy;

class ListStorageUnitsRequest extends APIBaseRequest {
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool {
        $policy = app(StorageUnitPolicy::class);
        $user = $this->user();
        return $policy->canView($user, null);
    }

    public function validationData()
    {
        return array_merge($this->all(), [
            'warehouse_id' => $this->route('warehouse_id')
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array {
        return [
            'warehouse_id' => 'required|string|exists:warehouses,id',
            'per_page' => 'sometimes|integer|min:1|max:100',
            'page' => 'sometimes|integer|min:1',
            'type_id' => 'sometimes|integer',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array {
        return [
            'warehouse_id.required' => 'Warehouse ID is required.',
            'warehouse_id.exists' => 'Warehouse ID does not exist.',
            'per_page.integer' => 'Items per page must be a number.',
            'per_page.min' => 'Items per page must be at least 1.',
            'per_page.max' => 'Items per page cannot exceed 100.',
            'page.integer' => 'Page number must be a number.',
            'page.min' => 'Page number must be at least 1.',
        ];
    }
}
