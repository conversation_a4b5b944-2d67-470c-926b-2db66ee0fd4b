<?php

namespace App\WarehouseManagement\Interfaces\Http\Requests;

use App\Http\Requests\APIBaseRequest;
use App\WarehouseManagement\Domain\Policies\StorageUnitPolicy;

class CreateStorageUnitRequest extends APIBaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $policy = app(StorageUnitPolicy::class);
        $user = $this->user();
        return $policy->canCreate($user);
    }

    public function validationData()
    {
        return array_merge($this->request->all(), [
            'warehouse_id' => $this->route('warehouse_id')
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'warehouse_id' => 'required|string|size:8|exists:warehouses,id',
            'storageUnits' => 'required|array',
            'storageUnits.*.quantity' => 'required|integer|min:1',
            'storageUnits.*.type_id' => 'required|string|exists:storage_unit_types,id'
        ];
    }

    public function messages(): array
    {
        return [
            // Warehouse validation messages
            'warehouse_id.required' => 'Warehouse ID is required.',
            'warehouse_id.string' => 'Warehouse ID must be a string.',
            'warehouse_id.size' => 'Invalid Wareghouse ID.',
            'warehouse_id.exists' => 'Warehouse ID does not exist.',
            
            // Array format validation messages
            'storageUnits.required' => 'Storage units are required.',
            'storageUnits.array' => 'Storage units must be an array.',

            'storageUnits.*.quantity.required' => 'Quantity is required.',
            'storageUnits.*.quantity.integer' => 'Quantity must be a number.',
            'storageUnits.*.quantity.min' => 'Quantity must be at least 1.',
            'storageUnits.*.type_id.required' => 'Type ID is required.',
            'storageUnits.*.type_id.integer' => 'Type ID must be a number.',
            'storageUnits.*.type_id.exists' => 'Type ID does not exist.'

        ];
    }
}
