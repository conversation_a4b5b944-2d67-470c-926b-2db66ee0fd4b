<?php

namespace App\WarehouseManagement\Interfaces\Http\Requests;

use App\Http\Requests\APIBaseRequest;
use App\WarehouseManagement\Domain\Policies\WarehousePolicy;

class ListWarehousesRequest extends APIBaseRequest {
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool {
        $user = auth()->user();
        return $user && app(WarehousePolicy::class)
            ->canView($user, null);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array {
        return [
            'per_page' => 'sometimes|integer|min:1|max:100',
            'page' => 'sometimes|integer|min:1',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array {
        return [
            'per_page.integer' => 'Items per page must be a number.',
            'per_page.min' => 'Items per page must be at least 1.',
            'per_page.max' => 'Items per page cannot exceed 100.',
            'page.integer' => 'Page number must be a number.',
            'page.min' => 'Page number must be at least 1.',
        ];
    }
}
