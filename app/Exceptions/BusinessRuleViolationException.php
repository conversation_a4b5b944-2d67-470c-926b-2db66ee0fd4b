<?php

namespace App\Exceptions;

use DomainException;

/**
 * Exception thrown when a business rule is violated.
 * 
 * This exception represents expected business logic violations that should not be logged
 * as errors since they are part of normal application flow (e.g., validation failures,
 * constraint violations, etc.).
 */
class BusinessRuleViolationException extends DomainException
{
    /**
     * Create a new business rule violation exception.
     *
     * @param string $message The violation message
     * @param int $code The error code (default: 0)
     * @param \Throwable|null $previous The previous exception
     */
    public function __construct(string $message = "", int $code = 0, ?\Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }
}
