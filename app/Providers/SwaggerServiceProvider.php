<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;
use L5Swagger\Http\Controllers\SwaggerController;
use L5Swagger\Http\Controllers\SwaggerAssetController;
use L5Swagger\Http\Middleware\Config as L5SwaggerConfig;

class SwaggerServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Only register Swagger routes in local environment
        if (app()->environment('local')) {
            $this->registerSwaggerRoutes();
        }
    }

    /**
     * Register Swagger routes manually.
     * These routes are protected by the SwaggerAccessMiddleware
     * which returns a 404 Not Found response in non-local environments.
     */
    protected function registerSwaggerRoutes(): void
    {
        // Get the documentation name from config
        $documentation = config('l5-swagger.default');

        // Get the routes configuration
        $config = config('l5-swagger.documentation' . $documentation);

        if (!$config) {
            return;
        }

        // Register the API documentation route
        if (isset($config['routes']['api'])) {
            Route::get($config['routes']['api'], [
                'as' => 'l5-swagger.' . $documentation . '.api',
                'middleware' => array_merge(['web', 'swagger.access'], $config['routes']['middleware']['api'] ?? []),
                'uses' => SwaggerController::class . '@api',
            ])->middleware(L5SwaggerConfig::class);
        }

        // Register the docs route
        Route::get('docs', [
            'as' => 'l5-swagger.' . $documentation . '.docs',
            'middleware' => array_merge(['web', 'swagger.access'], $config['routes']['middleware']['docs'] ?? []),
            'uses' => SwaggerController::class . '@docs',
        ])->middleware(L5SwaggerConfig::class);

        // Register the asset route
        Route::get('docs/asset/{asset}', [
            'as' => 'l5-swagger.' . $documentation . '.asset',
            'middleware' => array_merge(['web', 'swagger.access'], $config['routes']['middleware']['asset'] ?? []),
            'uses' => SwaggerAssetController::class . '@index',
        ])->middleware(L5SwaggerConfig::class);

        // Register the OAuth2 callback route
        if (isset($config['routes']['oauth2_callback'])) {
            Route::get($config['routes']['oauth2_callback'], [
                'as' => 'l5-swagger.' . $documentation . '.oauth2_callback',
                'middleware' => array_merge(['web', 'swagger.access'], $config['routes']['middleware']['oauth2_callback'] ?? []),
                'uses' => SwaggerController::class . '@oauth2Callback',
            ])->middleware(L5SwaggerConfig::class);
        }
    }
}
