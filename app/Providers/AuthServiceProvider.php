<?php

namespace App\Providers;

use App\Core\Auth\Guards\JwtGuard;
use App\Core\Auth\Services\AuthContext;
use App\Core\Auth\Services\PermissionService;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Auth;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        \App\WarehouseManagement\Domain\Entities\Warehouse::class => \App\WarehouseManagement\Domain\Policies\WarehousePolicy::class,
        \App\Settings\Domain\Entities\StorageUnitType::class => \App\Settings\Domain\Policies\StorageUnitTypePolicy::class,
        \App\Settings\Domain\Entities\Material::class => \App\Settings\Domain\Policies\MaterialPolicy::class,
    ];

    /**
     * Registers authentication and authorization services in the application container.
     *
     * Registers the PermissionService and AuthContext as singletons for use throughout the application.
     */
    public function register(): void
    {
        // Register the PermissionService as a singleton
        $this->app->singleton(PermissionService::class, function () {
            return new PermissionService();
        });

        // Register AuthContext as a singleton
        $this->app->singleton(AuthContext::class, function () {
            return new AuthContext();
        });
    }

    /**
     * Boots authentication and authorization services, including policy registration and custom guard setup.
     *
     * Registers model-to-policy mappings and adds a custom 'jwt' authentication guard using the JwtGuard and AuthContext.
     */
    public function boot(): void
    {
        // Register policies
        $this->registerPolicies();

        // Register custom JWT guard
        Auth::extend('jwt', function ($app, string $name, array $config) {
            return new JwtGuard($app->make(AuthContext::class));
        });
    }
}
