# wakilni-wms-api/.dockerignore

# Dependencies (will be installed in container)
node_modules/
vendor/

# Environment files
.env
.env.local
.env.testing
.env.example

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Storage (will be mounted as volumes)
storage/logs/*
storage/framework/cache/*
storage/framework/sessions/*
storage/framework/views/*

# Bootstrap cache
bootstrap/cache/*

# Testing
tests/
phpunit.xml
.phpunit.result.cache

# Documentation
README.md
docs/

# Docker files (avoid recursion)
Dockerfile*
docker-compose*.yml
.dockerignore

# Temporary files
*.tmp
*.temp
*.log