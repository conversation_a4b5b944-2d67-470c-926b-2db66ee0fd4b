# wakilni-wms-api/docker/nginx.conf

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    
    server {
        listen 8000;
        root /var/www/html/public;
        index index.php;

        client_max_body_size 100M;

        location / {
            try_files $uri $uri/ /index.php?$query_string;
        }

        location ~ \.php$ {
            fastcgi_pass 127.0.0.1:9000;
            fastcgi_index index.php;
            fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
            include fastcgi_params;
            fastcgi_read_timeout 300;
        }

        location /health {
            return 200 "healthy";
            add_header Content-Type text/plain;
        }

        location ~ /\.ht {
            deny all;
        }
    }
}