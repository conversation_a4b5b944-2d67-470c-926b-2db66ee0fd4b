# wakilni-wms-api/Dockerfile (Simplified - No Supervisor)
FROM php:8.2-fpm-alpine

# Install system dependencies
RUN apk add --no-cache \
    curl \
    libzip-dev \
    libpng-dev \
    mysql-client \
    bash \
    git \
    autoconf \
    make \
    g++

# Install PHP extensions
RUN docker-php-ext-install -j$(nproc) \
    pdo \
    pdo_mysql \
    zip \
    gd \
    bcmath

# Install Redis extension
RUN pecl install redis && docker-php-ext-enable redis

# Install Composer
COPY --from=composer:2 /usr/bin/composer /usr/bin/composer

# Create app user
RUN adduser -D -s /bin/bash www

# Set working directory
WORKDIR /var/www/html

# Copy composer files first
COPY composer.json composer.lock ./

# Install PHP dependencies
RUN composer install --no-scripts --no-dev --prefer-dist

# Copy all Laravel code
COPY . .

# Set permissions
RUN chown -R www:www /var/www/html \
    && chmod -R 755 storage bootstrap/cache

# Generate autoloader
RUN composer dump-autoload --optimize

# Create Laravel directories
RUN mkdir -p storage/logs storage/framework/{cache,sessions,views}

# Expose port
EXPOSE 8000

# Start PHP built-in server (simpler for development/testing)
CMD ["php", "artisan", "serve", "--host=0.0.0.0", "--port=8000"]